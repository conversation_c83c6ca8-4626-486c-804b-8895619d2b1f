import fs from 'fs'
import { setAdjust } from '../api/device.js'
import { setDevicePowerCurve } from '../service/setPowerCurve.js'

// console.log('curve data:', curveData)

export const initBeforeRun = async () => {
  // 储能1 soc 设置
  await setAdjust({
    "model": "pub_energy_storage",
    "device_id": 1,
    "col": "dcyt22",
    "value": 50
  })
  // 储能2 soc 设置
  await setAdjust({
    "model": "pub_energy_storage",
    "device_id": 2,
    "col": "dcyt22",
    "value": 50
  })
}

function runDelay(methods, interval) {
  let i = 0;
  (function execute() {
    if (i < methods.length) {
      methods[i]();
      i++;
      setTimeout(execute, interval);
    }
  })();
}

export const getDeviceRunData = async () => {
  const curveData = JSON.parse(fs.readFileSync('../backend/on_grid/1.json'))

  const { timestamps: ts, storage } = curveData.curves.combined
  const { wind, solar, electrolyzers } = curveData.curves.individual

  const timestamps = ts.map(v => new Date(v).getTime())
  // console.log('alk:', Array.isArray(curveData.curves.individual.solor) )
  // bat
  const bat_1_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: -storage[i]
    }
  })
  // alk
  const alk_15_1_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: electrolyzers[0].data[i]
    }
  })
  const alk_15_2_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: electrolyzers[1].data[i]
    }
  })
  const alk_10_1_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: electrolyzers[2].data[i]
    }
  })
  const alk_10_2_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: electrolyzers[3].data[i]
    }
  })
  // pv
  const pv_1_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: solar[i] / 2
    }
  })
  const pv_2_data = pv_1_data
  // wind
  const wind_1_data = timestamps.map((v, i) => {
    return {
      timestamp: v / 1000,
      value: wind[i] / 2
    }
  })
  const wind_2_data = wind_1_data
  return {
    bat_1_data,
    alk_15_1_data, alk_15_2_data, alk_10_1_data, alk_10_2_data,
    pv_1_data, pv_2_data,
    wind_1_data, wind_2_data
  }
}

export const runOngrid = async () => {
  const {
    bat_1_data,
    alk_15_1_data, alk_15_2_data, alk_10_1_data, alk_10_2_data,
    pv_1_data, pv_2_data,
    wind_1_data, wind_2_data
  } = await getDeviceRunData()

  const F_BAT_1 = () => setDevicePowerCurve('BAT_1', bat_1_data)
  const F_ALK_15_1 = () => setDevicePowerCurve('ALK_15_1', alk_15_1_data)
  const F_WIND_1 = () => setDevicePowerCurve('WIND_1', wind_1_data)
  const F_ALK_10_1 = () => setDevicePowerCurve('ALK_10_1', alk_10_1_data)
  const F_WIND_2 = () => setDevicePowerCurve('WIND_2', wind_2_data)
  const F_ALK_15_2 = () => setDevicePowerCurve('ALK_15_2', alk_15_2_data)
  const F_PV_1 = () => setDevicePowerCurve('PV_1', pv_1_data)
  const F_ALK_10_2 = () => setDevicePowerCurve('ALK_10_2', alk_10_2_data)
  const F_PV_2 = () => setDevicePowerCurve('PV_2', pv_2_data)
  
  // const queue = [F_BAT_1, F_ALK_15_1, F_WIND_1, F_ALK_10_1, F_WIND_2, F_ALK_15_2, F_PV_1, F_ALK_10_2, F_PV_2]
  const queue = [F_BAT_1]
  runDelay(queue, 3000)
}

// initBeforeRun()
runOngrid()
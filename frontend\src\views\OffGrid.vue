<template>
  <div class="off-grid">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>离网运行策略</h2>
          <p class="header-desc">风光储制氢系统离网运行仿真（储能15kWh/15kW）</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="success" 
            @click="runNewSimulation"
            :loading="runningSimulation"
            size="large"
          >
            <el-icon><Lightning /></el-icon>
            运行离网仿真
          </el-button>
        </div>
      </div>
    </div>

    <!-- 仿真选择器 -->
    <div class="simulation-selector">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>离网仿真结果选择</span>
            <el-button 
              text 
              @click="loadSimulations"
              :loading="loadingList"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        
        <div v-if="simulations.length === 0" class="empty-state">
          <el-empty description="暂无离网仿真结果">
            <el-button type="success" @click="runNewSimulation">
              运行第一个离网仿真
            </el-button>
          </el-empty>
        </div>
        
        <div v-else>
          <el-select 
            v-model="selectedSimulationId" 
            placeholder="选择离网仿真结果"
            style="width: 400px"
            @change="onSimulationChange"
          >
            <el-option
              v-for="sim in simulations"
              :key="sim.id"
              :label="`离网仿真 #${sim.id} - ${formatDate(sim.timestamp)}`"
              :value="sim.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>离网仿真 #{{ sim.id }}</span>
                <span style="color: #8492a6; font-size: 12px;">
                  {{ formatDate(sim.timestamp) }}
                </span>
              </div>
            </el-option>
          </el-select>
          
          <!-- 仿真信息 -->
          <div v-if="selectedSimulation" class="simulation-info">
            <el-descriptions :column="4" size="small" border>
              <el-descriptions-item label="产氢量">
                {{ selectedSimulation.hydrogenProduction?.toFixed(2) || 0 }} kg
              </el-descriptions-item>
              <el-descriptions-item label="绿电利用">
                {{ (selectedSimulation.totalRenewableEnergy || 0).toFixed(1) }} kWh
              </el-descriptions-item>
              <el-descriptions-item label="绿电利用率">
                100% (离网模式)
              </el-descriptions-item>
              <el-descriptions-item label="平均储能SOC">
                {{ (selectedSimulation.averageStorageSoc || 0).toFixed(1) }}%
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表组件 -->
    <div v-if="selectedSimulationId" class="chart-section">
      <OffGridChart :simulation-id="selectedSimulationId" />
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-chart">
      <el-card>
        <el-empty description="请选择一个离网仿真结果查看图表" />
      </el-card>
    </div>

    <!-- 运行仿真对话框 -->
    <el-dialog
      v-model="showRunDialog"
      title="运行离网仿真"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-alert
        title="离网模式配置说明"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      >
        <template #default>
          <p style="margin: 0;">离网模式下储能配置固定为：容量15kWh，最大充放电功率15kW</p>
          <p style="margin: 5px 0 0 0;">系统完全依靠绿电和储能运行，无电网后备支持</p>
        </template>
      </el-alert>
      
      <el-form :model="simulationConfig" label-width="120px">
        <el-form-item label="仿真天数">
          <el-input-number 
            v-model="simulationConfig.days" 
            :min="1" 
            :max="30"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="风机容量">
          <el-input-number 
            v-model="simulationConfig.windCapacity" 
            :min="1" 
            :max="100"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">kW</span>
        </el-form-item>
        <el-form-item label="光伏容量">
          <el-input-number 
            v-model="simulationConfig.solarCapacity" 
            :min="1" 
            :max="100"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">kW</span>
        </el-form-item>
        <el-form-item label="储能容量">
          <el-input-number
            v-model="fixedStorageCapacity"
            :disabled="true"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">kWh (离网模式固定)</span>
        </el-form-item>
        <el-form-item label="储能功率">
          <el-input-number
            v-model="fixedStoragePower"
            :disabled="true"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #909399;">kW (离网模式固定)</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRunDialog = false">取消</el-button>
          <el-button 
            type="success" 
            @click="confirmRunSimulation"
            :loading="runningSimulation"
          >
            开始离网仿真
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Lightning, Refresh } from '@element-plus/icons-vue'
import OffGridChart from '../components/OffGridChart.vue'
import { simulationApi } from '../api/simulation'

// 响应式数据
const simulations = ref([])
const selectedSimulationId = ref(null)
const loadingList = ref(false)
const runningSimulation = ref(false)
const showRunDialog = ref(false)

// 仿真配置
const simulationConfig = ref({
  days: 7,
  windCapacity: 30,
  solarCapacity: 30
  // 储能配置在离网模式下固定为15kWh/15kW
})

// 固定的储能配置值（用于显示）
const fixedStorageCapacity = ref(15)
const fixedStoragePower = ref(15)

// 计算属性
const selectedSimulation = computed(() => {
  return simulations.value.find(s => s.id === selectedSimulationId.value)
})

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载仿真列表
const loadSimulations = async () => {
  loadingList.value = true
  try {
    const response = await simulationApi.getOffgridSimulations()
    simulations.value = response.data
    
    // 如果没有选中的仿真且有数据，自动选择最新的
    if (!selectedSimulationId.value && simulations.value.length > 0) {
      selectedSimulationId.value = simulations.value[0].id
    }
  } catch (error) {
    console.error('加载离网仿真列表失败:', error)
    ElMessage.error('加载离网仿真列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loadingList.value = false
  }
}

// 仿真选择变化
const onSimulationChange = (id) => {
  selectedSimulationId.value = id
}

// 运行新仿真
const runNewSimulation = () => {
  showRunDialog.value = true
}

// 确认运行仿真
const confirmRunSimulation = async () => {
  try {
    runningSimulation.value = true
    
    const config = {
      simulation: {
        days: simulationConfig.value.days
      },
      windTurbine: {
        capacity: simulationConfig.value.windCapacity
      },
      solarPanel: {
        capacity: simulationConfig.value.solarCapacity
      }
      // 储能和电网配置由后端离网模式自动设置
    }
    
    const response = await simulationApi.runOffgridSimulation(config)
    
    ElMessage.success('离网仿真运行完成！')
    showRunDialog.value = false
    
    // 重新加载仿真列表
    await loadSimulations()
    
    // 自动选择新生成的仿真结果
    if (response.data.fileId) {
      selectedSimulationId.value = response.data.fileId
    }
    
  } catch (error) {
    console.error('运行离网仿真失败:', error)
    ElMessage.error('运行离网仿真失败: ' + (error.response?.data?.message || error.message))
  } finally {
    runningSimulation.value = false
  }
}

// 初始化
onMounted(() => {
  loadSimulations()
})
</script>

<style scoped>
.off-grid {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-left: 4px solid #67c23a;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.simulation-selector {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.simulation-info {
  margin-top: 15px;
}

.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>

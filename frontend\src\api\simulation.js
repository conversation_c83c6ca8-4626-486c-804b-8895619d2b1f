import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.config.url)
    return response
  },
  error => {
    console.error('响应错误:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

export const simulationApi = {
  // 并网仿真相关
  // 获取并网仿真结果
  getSimulation(id) {
    return api.get(`/simulation/${id}`)
  },

  // 获取并网仿真列表
  getSimulations() {
    return api.get('/simulations')
  },

  // 运行并网仿真
  runSimulation(config = {}) {
    return api.post('/simulation/run', { config })
  },

  // 获取并网默认配置
  getConfig() {
    return api.get('/config')
  },

  // 离网仿真相关
  // 获取离网仿真结果
  getOffgridSimulation(id) {
    return api.get(`/simulation/offgrid/${id}`)
  },

  // 获取离网仿真列表
  getOffgridSimulations() {
    return api.get('/simulations/offgrid')
  },

  // 运行离网仿真
  runOffgridSimulation(config = {}) {
    return api.post('/simulation/offgrid/run', { config })
  },

  // 获取离网默认配置
  getOffgridConfig() {
    return api.get('/config/offgrid')
  },

  // 健康检查
  health() {
    return api.get('/health')
  }
}

export default api

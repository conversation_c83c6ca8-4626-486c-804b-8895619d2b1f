import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl, getDeviceAndCol
} from '../api/device.js'



/**
 * 1. 查询可用设备及测点
 */
export async function getDeviceAndColData() {
  const { code, data, msg } = await getDeviceAndCol()
  if ( code === 0) {
    console.log('Get Device and Col Data:', JSON.stringify(data, null, 2))
  } else {
    console.error('Err:', msg)
  }
}

getDeviceAndColData()

import request from './request.js';

export const urlPrefix = '/api/v3/mgec/extra';

export async function get(url, params) {
  return request({
    method: 'get',
    url,
    params
  }) 
}

export async function post(url, data, headers) {
  return request({
    method: 'post',
    url,
    data,
    headers
  })
}

export async function del(url, data, headers) {
  return request({
    method: 'delete',
    url,
    data,
    headers
  })
}



## 并离网控制策略
一、风光储网制氢-并网策略验证：
- 基于预设的风光功率曲线
- 绿电功率充足(均分后所有电解槽功率 >= 30%)电解槽(最大程度)均分功率, 有剩余的绿电，则储能、电网按先后优先级消纳
- 绿电功率不足(均分后所有电解槽功率 < 30%), 则储能、电网按先后优先级供电，所有电解槽以最小(30%)功率运行。


二、 离网下策略验证：
- 基于预设的风光功率曲线
- 绿电功率充足(均分后所有电解槽功率 >= 30%)电解槽(最大程度)均分功率, 有剩余的绿电，则储能充电
- 绿电功率不足(均分后所有电解槽功率 < 30%), 则储能供电，如果还是不足，最小数量的电解槽停机，其余保持30%功率运行。


三、注意项：
- 储能SOC上下限 10%~90%
- 并网下电网暂不考虑上下电约束
- 电解槽功率范围 30%~100%
- 初始状态都为开机状态
- 离网下，考虑电解槽冷启动时间


四、
一、对应的的API操作：
- 风机、光伏、储能、电解槽、电网(被动)功率曲线下发
- 风光储网电解槽模拟器最大功率设置
- 储能初始SOC，最大充放电功率设置，充放电api，读取电池当前soc判断上下限
- 电解槽启停，功率下发
- 获取风机，光伏，储能，电网，电解槽功率实时/历史曲线数据/电量统计数据

二、基于设备的API实现功率曲线下发
1. 运行逻辑，条件判断，运行相关的伪代码
2. 执行先后顺序，各个阶段调用的 api及传参

## 策略下发
## 基础
- 风机(预测)功率下发
- 

### 日前
- 储能运行曲线
- 4台电解槽运行曲线
- 电网(间接)功率曲线

### 日内
- 1. 获取风光功率曲线
- 2. 获取实时数据控制
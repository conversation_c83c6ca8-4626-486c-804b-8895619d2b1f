<template>
  <div id="app">
    <el-container>
      <!-- 头部导航 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="header-left">
            <h1>风光储网制氢策略仿真测试</h1>
            <el-menu
              :default-active="$route.path"
              mode="horizontal"
              router
              class="nav-menu"
              :unique-opened="false"
            >
              <el-menu-item index="/grid-connected">
                并网策略
              </el-menu-item>
              <el-menu-item index="/off-grid">
                离网策略
              </el-menu-item>
              
            </el-menu>
          </div>
        </div>
      </el-header>

      <!-- 主体内容 -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { Link as Connection, Lightning } from '@element-plus/icons-vue'
</script>

<style scoped>
#app {
  height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 60px;
}

.header-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 40px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  white-space: nowrap;
}

.nav-menu {
  background: transparent !important;
  border: none !important;
}

.nav-menu :deep(.el-menu-item) {
  color: rgba(255, 255, 255, 0.8) !important;
  border-bottom: 2px solid transparent !important;
  font-size: 16px;
  font-weight: 500;
}

.nav-menu :deep(.el-menu-item:hover) {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.nav-menu :deep(.el-menu-item.is-active) {
  color: white !important;
  border-bottom-color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.app-main {
  background: #f5f7fa;
  padding: 0;
  height: calc(100vh - 60px);
  overflow-y: auto;
}
</style>

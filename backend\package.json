{"name": "device-api-backend", "version": "1.0.0", "description": "Wind-Solar-Storage-Grid-Hydrogen Backend API", "main": "app.js", "scripts": {"start": "node app.js", "simulate": "node simulation.js", "dev": "nodemon app.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["energy", "simulation", "hydrogen", "renewable"], "author": "", "license": "MIT"}
const fs = require('fs');
const path = require('path');
const days7_15 = require('./data/baseData_7days_15min.json');
const days1_15 = require('./data/baseData_1days_15min.json');

// console.log('数据文件加载完成');
// console.log('1天数据点数:', days1_15.metadata.totalPoints);
// console.log('7天数据点数:', days7_15.metadata.totalPoints);
// ========== 设备参数配置 ==========
const CONFIG = {
  // 仿真时间配置
  simulation: {
    days: 1,                    // 仿真天数 [可配置]
    timeStep: 15,               // 时间步长(分钟)
  },

  // 风机配置 
  windTurbine: {
    capacity: 30                // kW 容量 [可配置]
  },

  // 光伏配置 
  solarPanel: {
    capacity: 30                // kW 容量 [可配置]
  },

  // 储能配置 
  energyStorage: {
    capacity: 30,               // kWh 容量 [可配置]
    initialSoc: 50,             // % 初始SOC [可配置]
    socMin: 10,                 // % SOC下限 [可配置]
    socMax: 90,                 // % SOC上限 [可配置]
    cRate: 1                    // 电倍率：1，即最大充放电功率 30kW
  },

  // 电网配置 
  grid: {
    powerLimit: 20              // kW 上下网功率限制 [可配置]
  },

  // 电解槽配置
  electrolyzers: [
    { id: 1, capacity: 15, name: '电解槽1', initialState: 'stopped' },
    { id: 2, capacity: 15, name: '电解槽2', initialState: 'stopped' },
    { id: 3, capacity: 10, name: '电解槽3', initialState: 'stopped' },
    { id: 4, capacity: 10, name: '电解槽4', initialState: 'stopped' }
  ],

  electrolyzerConfig: {
    minPowerRatio: 30,          // % 功率范围：30%~100%
    maxPowerRatio: 100,         // % 功率范围：30%~100%
    standbyPowerRatio: 10,      // % 热备：保持10%功率即可
    energyConsumption: 4.3,     // kWh/Nm³ 能耗 [可配置]
    coldStartTime: 30           // 分钟 冷启动时间：30min
  }
};



// ========== 电解槽控制逻辑 ==========
// 严格按照并网策略.md中的控制策略实现

// 电解槽状态枚举
const ElectrolyzerState = {
  STOPPED: 'stopped',           // 停机：功率 < 10%，功率为0
  COLD_START: 'cold_start',     // 冷启动：关机->正常运行，30min功率爬坡
  STANDBY: 'standby',           // 热备：保持10%功率，产氢量不计
  RUNNING: 'running'            // 正常运行：30% <= 功率 <= 100%
};

// 初始化电解槽状态
function initializeElectrolyzers(config) {
  return config.electrolyzers.map(e => ({
    ...e,
    state: e.initialState === 'running' ? ElectrolyzerState.RUNNING : ElectrolyzerState.STOPPED,
    currentPower: 0,
    targetPower: 0,
    coldStartTimer: 0,
    hydrogenProduction: 0
  }));
}

/**
 * 电解槽群控策略
 * 控制策略：
 * 1. 计算总可用功率（绿电+储能+电网）
 * 2. 严格等比例分配功率给所有电解槽
 * 3. 如果均分后功率 >= 30%，所有电解槽等比例运行
 * 4. 如果均分后功率 < 30%，小功率电解槽优先热备或停机
 */
function allocateElectrolyzerPower(totalAvailablePower, electrolyzers, config) {
  const totalCapacity = electrolyzers.reduce((sum, e) => sum + e.capacity, 0);
  const minPowerRatio = config.electrolyzerConfig.minPowerRatio / 100; // 30%
  const standbyPowerRatio = config.electrolyzerConfig.standbyPowerRatio / 100; // 10%
  const maxPowerRatio = config.electrolyzerConfig.maxPowerRatio / 100; // 100%

  // 重置所有电解槽的目标功率
  electrolyzers.forEach(e => e.targetPower = 0);

  // 计算如果所有电解槽等比例分配的功率比例
  const equalPowerRatio = totalAvailablePower / totalCapacity;

  if (equalPowerRatio >= minPowerRatio) {
    // 功率充足：所有电解槽等比例分配
    const actualRatio = Math.min(equalPowerRatio, maxPowerRatio);
    electrolyzers.forEach(e => {
      e.targetPower = e.capacity * actualRatio;
    });

    return {
      totalAllocated: electrolyzers.reduce((sum, e) => sum + e.targetPower, 0),
      strategy: 'equal_distribution',
      message: `功率充足，所有电解槽等比例分配 ${(actualRatio * 100).toFixed(1)}%`
    };
  } else {
    // 功率不足：需要部分电解槽热备或停机
    // 按容量从小到大排序（小功率的先热备/停机）
    const sortedElectrolyzers = [...electrolyzers].sort((a, b) => a.capacity - b.capacity);
    let runningElectrolyzers = [];
    let standbyElectrolyzers = [];
    let stoppedElectrolyzers = [];

    // 逐步减少运行的电解槽数量，直到剩余的能够等比例运行在30%以上
    // 从所有电解槽开始，逐步移除小功率的电解槽
    let candidateElectrolyzers = [...electrolyzers];
    let availablePowerForRunning = totalAvailablePower;

    while (candidateElectrolyzers.length > 0) {
      const candidateCapacity = candidateElectrolyzers.reduce((sum, e) => sum + e.capacity, 0);
      const requiredMinPower = candidateCapacity * minPowerRatio;

      if (availablePowerForRunning >= requiredMinPower) {
        // 找到了可以运行的电解槽组合
        runningElectrolyzers = [...candidateElectrolyzers];

        // 为运行的电解槽等比例分配功率
        const runningPowerRatio = availablePowerForRunning / candidateCapacity;
        runningElectrolyzers.forEach(e => {
          e.targetPower = e.capacity * Math.min(runningPowerRatio, maxPowerRatio);
        });

        // 处理剩余的电解槽（热备或停机）
        const remainingElectrolyzers = sortedElectrolyzers.filter(e => !runningElectrolyzers.includes(e));
        let availablePowerForStandby = totalAvailablePower - runningElectrolyzers.reduce((sum, e) => sum + e.targetPower, 0);

        for (const e of remainingElectrolyzers) {
          const standbyPower = e.capacity * standbyPowerRatio;
          if (availablePowerForStandby >= standbyPower) {
            standbyElectrolyzers.push(e);
            e.targetPower = standbyPower;
            availablePowerForStandby -= standbyPower;
          } else {
            stoppedElectrolyzers.push(e);
            e.targetPower = 0;
          }
        }
        break;
      } else {
        // 移除最小的电解槽，为其预留热备功率
        if (candidateElectrolyzers.length === 0) {
          break; // 没有更多电解槽可以移除
        }

        const smallestElectrolyzer = sortedElectrolyzers.find(e => candidateElectrolyzers.includes(e));
        if (smallestElectrolyzer) {
          candidateElectrolyzers = candidateElectrolyzers.filter(e => e !== smallestElectrolyzer);
          // 为热备预留功率
          const standbyPower = smallestElectrolyzer.capacity * standbyPowerRatio;
          availablePowerForRunning -= standbyPower;
        } else {
          // 找不到匹配的电解槽，退出循环
          break;
        }
      }
    }

    // 如果没有找到合适的组合，所有电解槽都热备或停机
    if (runningElectrolyzers.length === 0) {
      let availablePowerForStandby = totalAvailablePower;
      for (const e of sortedElectrolyzers) {
        const standbyPower = e.capacity * standbyPowerRatio;
        if (availablePowerForStandby >= standbyPower) {
          standbyElectrolyzers.push(e);
          e.targetPower = standbyPower;
          availablePowerForStandby -= standbyPower;
        } else {
          stoppedElectrolyzers.push(e);
          e.targetPower = 0;
        }
      }
    }

    const totalAllocated = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);

    return {
      totalAllocated: totalAllocated,
      strategy: 'priority_allocation',
      message: `功率不足，${runningElectrolyzers.length}台运行，${standbyElectrolyzers.length}台热备，${stoppedElectrolyzers.length}台停机`,
      details: {
        running: runningElectrolyzers.length,
        standby: standbyElectrolyzers.length,
        stopped: stoppedElectrolyzers.length
      }
    };
  }
}

// ========== 主仿真函数 ==========
function runSimulation(config) {
  console.log('开始加载基础数据...');
  console.log('配置天数:', config.simulation.days);

  // 加载基础数据（风光功率曲线和分时电价）
  const baseData = config.simulation.days === 1 ? days1_15 : days7_15;

  const results = {
    timestamps: baseData.timestamps,
    windPower: [],
    solarPower: [],
    totalRenewable: [],
    electrolyzerPower: [],
    electrolyzerIndividual: config.electrolyzers.map(() => []),
    storagePower: [],
    storageSoc: [],
    gridPower: [],
    hydrogenProduction: [],
    electricityPrice: baseData.electricityPrice
  };

  // 初始化设备状态
  let electrolyzers = initializeElectrolyzers(config);
  let storageSoc = config.energyStorage.initialSoc;
  let totalHydrogen = 0;

  console.log(`开始仿真，共 ${baseData.timestamps.length} 个时间点...`);
  
  // 仿真循环
  for (let t = 0; t < baseData.timestamps.length; t++) {
    // 计算实际功率（标幺值 × 容量）
    const windPower = baseData.windPowerCurve[t] * config.windTurbine.capacity;
    const solarPower = baseData.solarPowerCurve[t] * config.solarPanel.capacity;
    const totalRenewable = windPower + solarPower;

    results.windPower.push(windPower);
    results.solarPower.push(solarPower);
    results.totalRenewable.push(totalRenewable);

    // 先记录当前时刻的SOC（反映上一时刻的储能功率影响）
    results.storageSoc.push(storageSoc);

    // 计算储能最大可放电功率
    const availableEnergy = (storageSoc - config.energyStorage.socMin) / 100 * config.energyStorage.capacity; // kWh
    const maxDischargeByEnergy = availableEnergy / (config.simulation.timeStep / 60); // kW
    const maxStorageDischarge = Math.min(
      config.energyStorage.capacity * config.energyStorage.cRate, // 最大放电功率 = 容量 * 电倍率
      maxDischargeByEnergy
    );

    // 计算储能最大可充电功率
    const availableCapacity = (config.energyStorage.socMax - storageSoc) / 100 * config.energyStorage.capacity; // kWh
    const maxChargeByCapacity = availableCapacity / (config.simulation.timeStep / 60); // kW
    const maxStorageCharge = Math.min(
      config.energyStorage.capacity * config.energyStorage.cRate, // 最大充电功率 = 容量 * 电倍率
      maxChargeByCapacity
    );

    // 第一步：计算电解槽总容量和最低功率需求
    const totalCapacity = electrolyzers.reduce((sum, e) => sum + e.capacity, 0);
    const minTotalPower = totalCapacity * (config.electrolyzerConfig.minPowerRatio / 100); // 所有电解槽30%总功率

    // 检查当前是否处于冷启动阶段
    const coldStartElectrolyzers = electrolyzers.filter(e => e.state === ElectrolyzerState.COLD_START);
    const stoppedElectrolyzers = electrolyzers.filter(e => e.state === ElectrolyzerState.STOPPED);

    // 预判：如果有停机电解槽且绿电足够，它们会进入冷启动状态
    const minPowerPerElectrolyzer = totalCapacity * (config.electrolyzerConfig.minPowerRatio / 100) / electrolyzers.length;
    const willEnterColdStart = stoppedElectrolyzers.length > 0 && totalRenewable >= minPowerPerElectrolyzer;

    // 判断是否处于冷启动阶段（已有冷启动的或即将进入冷启动的）
    const isInColdStartPhase = coldStartElectrolyzers.length > 0 || willEnterColdStart;

    // 调试信息（仅第一时刻）
    if (t === 0) {
      console.log(`第一时刻调试信息:`);
      console.log(`- 绿电功率: ${totalRenewable.toFixed(2)} kW`);
      console.log(`- 最低启动功率: ${minPowerPerElectrolyzer.toFixed(2)} kW`);
      console.log(`- 停机电解槽数量: ${stoppedElectrolyzers.length}`);
      console.log(`- 冷启动电解槽数量: ${coldStartElectrolyzers.length}`);
      console.log(`- 即将进入冷启动: ${willEnterColdStart}`);
      console.log(`- 处于冷启动阶段: ${isInColdStartPhase}`);
    }

    let storagePower = 0;
    let gridPower = 0;
    let totalElectrolyzerDemand = 0;

    // 冷启动阶段特殊处理：储能功率为0，绿电优先，电网补充不足
    if (isInColdStartPhase) {
      storagePower = 0; // 冷启动阶段储能不充电也不放电

      // 计算实际可用功率（仅绿电）
      const availablePowerForElectrolyzers = totalRenewable;

      // 电网补充不足部分到30%最低需求
      if (availablePowerForElectrolyzers < minTotalPower) {
        const gridNeed = minTotalPower - availablePowerForElectrolyzers;
        gridPower = Math.min(gridNeed, config.grid.powerLimit);
      }

      // 计算实际总可用功率（绿电 + 电网）
      const totalAvailablePower = totalRenewable + gridPower;

      // 分配电解槽功率
      const powerRatio = Math.min(totalAvailablePower / totalCapacity, config.electrolyzerConfig.maxPowerRatio / 100);
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });
      totalElectrolyzerDemand = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);
    } else {
      // 正常运行阶段：按原有逻辑处理
      if (totalRenewable >= minTotalPower) {
      // 绿电足够让所有电解槽达到30%以上
      // 第二步：电解槽尽量多用绿电（30%-100%之间）
      const maxElectrolyzerPower = totalCapacity * (config.electrolyzerConfig.maxPowerRatio / 100);
      totalElectrolyzerDemand = Math.min(totalRenewable, maxElectrolyzerPower);

      // 等比例分配给所有电解槽
      const powerRatio = totalElectrolyzerDemand / totalCapacity;
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });

      // 第三步：剩余绿电优先储能充电
      const surplusPower = totalRenewable - totalElectrolyzerDemand;
      if (surplusPower > 0) {
        const chargePower = Math.min(surplusPower, maxStorageCharge);
        storagePower = -chargePower; // 充电为负

        // 第四步：储能充满后剩余电力上网
        const remainingSurplus = surplusPower - chargePower;
        if (remainingSurplus > 0) {
          gridPower = -Math.min(remainingSurplus, config.grid.powerLimit); // 上网为负
        }
      }
    } else {
      // 绿电不足，需要储能和电网补充到30%
      const powerShortage = minTotalPower - totalRenewable;

      // 第二步：储能优先补充
      const storageSupply = Math.min(powerShortage, maxStorageDischarge);
      storagePower = storageSupply; // 放电为正

      // 第三步：电网补充剩余缺口
      const remainingShortage = powerShortage - storageSupply;
      const gridSupply = Math.min(remainingShortage, config.grid.powerLimit);
      gridPower = gridSupply; // 下网为正

      // 第四步：计算实际可用功率并分配电解槽
      const actualAvailablePower = totalRenewable + storageSupply + gridSupply;

      // 简化版本：直接等比例分配，避免复杂的while循环
      const powerRatio = Math.min(actualAvailablePower / totalCapacity, config.electrolyzerConfig.maxPowerRatio / 100);
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });
      totalElectrolyzerDemand = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);
    }
    } // 结束正常运行阶段的else分支

    // 第三步：更新电解槽状态和计算产氢量
    let totalElectrolyzerPower = 0;
    let stepHydrogen = 0;

    electrolyzers.forEach((e, index) => {
      // 更新电解槽状态和功率
      updateElectrolyzerState(e, config.electrolyzerConfig, config.simulation.timeStep);
      totalElectrolyzerPower += e.currentPower;

      // 计算产氢量（只有正常运行状态才产氢）
      if (e.state === ElectrolyzerState.RUNNING) {
        const hydrogenRate = e.currentPower / config.electrolyzerConfig.energyConsumption;
        const hydrogenStep = hydrogenRate * (config.simulation.timeStep / 60); // Nm³
        stepHydrogen += hydrogenStep * 0.089; // 转换为kg (1Nm³ ≈ 0.089kg)
        e.hydrogenProduction += hydrogenStep * 0.089;
      }

      results.electrolyzerIndividual[index].push(e.currentPower);
    });

    results.electrolyzerPower.push(totalElectrolyzerPower);
    totalHydrogen += stepHydrogen;
    results.hydrogenProduction.push(totalHydrogen);
    
    // 第四步：储能和电网功率已在上面确定，这里只需要更新SOC

    // 更新储能SOC
    if (storagePower > 0) {
      // 放电（正数）
      storageSoc -= (storagePower * (config.simulation.timeStep / 60) / config.energyStorage.capacity) * 100; // 效率100%
    } else if (storagePower < 0) {
      // 充电（负数）
      storageSoc += (-storagePower * (config.simulation.timeStep / 60) / config.energyStorage.capacity) * 100; // 效率100%
    }

    // 确保SOC在合理范围内
    storageSoc = Math.max(config.energyStorage.socMin, Math.min(config.energyStorage.socMax, storageSoc));
    
    results.storagePower.push(storagePower);
    results.gridPower.push(gridPower);
  }
  
  return results;
}

// 更新电解槽状态
function updateElectrolyzerState(electrolyzer, config, timeStep) {
  const minPower = electrolyzer.capacity * (config.minPowerRatio / 100);
  const standbyPower = electrolyzer.capacity * (config.standbyPowerRatio / 100);
  
  // 状态转换逻辑
  switch (electrolyzer.state) {
    case ElectrolyzerState.STOPPED:
      if (electrolyzer.targetPower >= minPower) {
        electrolyzer.state = ElectrolyzerState.COLD_START;
        electrolyzer.coldStartTimer = config.coldStartTime;
        electrolyzer.currentPower = 0;
      }
      break;
      
    case ElectrolyzerState.COLD_START:
      electrolyzer.coldStartTimer -= timeStep;
      if (electrolyzer.coldStartTimer <= 0) {
        electrolyzer.state = ElectrolyzerState.RUNNING;
        electrolyzer.currentPower = Math.max(minPower, electrolyzer.targetPower);
      } else {
        // 冷启动期间功率爬坡
        const progress = 1 - (electrolyzer.coldStartTimer / config.coldStartTime);
        electrolyzer.currentPower = minPower * progress;
      }
      break;
      
    case ElectrolyzerState.RUNNING:
      if (electrolyzer.targetPower < minPower) {
        if (electrolyzer.targetPower >= standbyPower) {
          electrolyzer.state = ElectrolyzerState.STANDBY;
          electrolyzer.currentPower = standbyPower;
        } else {
          electrolyzer.state = ElectrolyzerState.STOPPED;
          electrolyzer.currentPower = 0;
        }
      } else {
        electrolyzer.currentPower = electrolyzer.targetPower;
      }
      break;
      
    case ElectrolyzerState.STANDBY:
      if (electrolyzer.targetPower >= minPower) {
        electrolyzer.state = ElectrolyzerState.RUNNING;
        electrolyzer.currentPower = electrolyzer.targetPower;
      } else if (electrolyzer.targetPower < standbyPower) {
        electrolyzer.state = ElectrolyzerState.STOPPED;
        electrolyzer.currentPower = 0;
      } else {
        electrolyzer.currentPower = standbyPower;
      }
      break;
  }
}

// ========== 输出文件管理 ==========
function getNextFileId() {
  const outputDir = path.join(__dirname, 'on_grid');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const files = fs.readdirSync(outputDir)
    .filter(file => file.endsWith('.json'))
    .map(file => parseInt(file.replace('.json', '')))
    .filter(id => !isNaN(id));
  
  return files.length > 0 ? Math.max(...files) + 1 : 1;
}

function saveResults(results, config) {
  const fileId = getNextFileId();
  const outputPath = path.join(__dirname, 'on_grid', `${fileId}.json`);
  
  // 计算统计数据
  const totalRenewableEnergy = results.totalRenewable.reduce((sum, power) => 
    sum + power * (config.simulation.timeStep / 60), 0);
  const totalGridEnergy = results.gridPower.reduce((sum, power) => 
    sum + Math.abs(power) * (config.simulation.timeStep / 60), 0);
  const gridExchangeRatio = totalGridEnergy / totalRenewableEnergy * 100;
  
  const output = {
    id: fileId,
    timestamp: new Date().toISOString(),
    config: config,
    curves: {
      combined: {
        timestamps: results.timestamps,
        windSolar: results.totalRenewable,
        electrolyzer: results.electrolyzerPower,
        storage: results.storagePower,
        grid: results.gridPower,
        storageSoc: results.storageSoc
      },
      individual: {
        wind: results.windPower,
        solar: results.solarPower,
        electrolyzers: results.electrolyzerIndividual.map((data, index) => ({
          name: config.electrolyzers[index].name,
          data: data
        }))
      }
    },
    statistics: {
      hydrogenProduction: results.hydrogenProduction[results.hydrogenProduction.length - 1],
      totalRenewableEnergy: totalRenewableEnergy,
      totalGridEnergy: totalGridEnergy,
      gridExchangeRatio: gridExchangeRatio,
      averageStorageSoc: results.storageSoc.reduce((sum, soc) => sum + soc, 0) / results.storageSoc.length
    }
  };
  
  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  console.log(`仿真结果已保存到: ${outputPath}`);
  console.log(`文件ID: ${fileId}`);
  console.log(`产氢量: ${output.statistics.hydrogenProduction.toFixed(2)} kg`);
  console.log(`上下网电量占比: ${output.statistics.gridExchangeRatio.toFixed(2)}%`);
  
  return fileId;
}

// ========== 主程序 ==========
function main() {
  console.log('开始运行风光储网制氢仿真...');
  console.log('配置参数:', JSON.stringify(CONFIG, null, 2));
  
  const results = runSimulation(CONFIG);
  const fileId = saveResults(results, CONFIG);
  
  console.log('仿真完成！');
  return fileId;
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('开始执行main函数...');
  try {
    main();
    console.log('main函数执行完成');
  } catch (error) {
    console.error('main函数执行出错:', error);
  }
}

module.exports = { runSimulation, CONFIG, main, saveResults };

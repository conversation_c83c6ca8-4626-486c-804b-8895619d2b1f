const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { runSimulation, CONFIG, main, saveResults } = require('./simulation');
const { runOffgridSimulation, CONFIG_OFFGRID, mainOffgrid, saveOffgridResults } = require('./simulation-offgrid');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 静态文件服务 - 提供output目录下的JSON文件
app.use('/on_grid', express.static(path.join(__dirname, 'on_grid')));
// 静态文件服务 - 提供off_grid目录下的JSON文件
app.use('/off_grid', express.static(path.join(__dirname, 'off_grid')));

// API路由

// 获取仿真结果
app.get('/api/simulation/:id', (req, res) => {
  try {
    const id = req.params.id;
    const filePath = path.join(__dirname, 'on_grid', `${id}.json`);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ 
        error: '仿真结果不存在',
        message: `找不到ID为 ${id} 的仿真结果文件` 
      });
    }
    
    const data = fs.readFileSync(filePath, 'utf8');
    const result = JSON.parse(data);
    
    res.json(result);
  } catch (error) {
    console.error('获取仿真结果失败:', error);
    res.status(500).json({ 
      error: '服务器内部错误',
      message: error.message 
    });
  }
});

// 获取所有仿真结果列表
app.get('/api/simulations', (req, res) => {
  try {
    const outputDir = path.join(__dirname, 'on_grid');
    
    if (!fs.existsSync(outputDir)) {
      return res.json([]);
    }
    
    const files = fs.readdirSync(outputDir)
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const id = parseInt(file.replace('.json', ''));
        const filePath = path.join(outputDir, file);
        const stats = fs.statSync(filePath);
        
        try {
          const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          return {
            id: id,
            timestamp: data.timestamp,
            config: {
              days: data.config.simulation.days,
              windCapacity: data.config.windTurbine.capacity,
              solarCapacity: data.config.solarPanel.capacity,
              storageCapacity: data.config.energyStorage.capacity
            },
            statistics: data.statistics,
            fileSize: stats.size,
            createdAt: stats.mtime
          };
        } catch (error) {
          console.error(`解析文件 ${file} 失败:`, error);
          return null;
        }
      })
      .filter(item => item !== null)
      .sort((a, b) => b.id - a.id); // 按ID降序排列
    
    res.json(files);
  } catch (error) {
    console.error('获取仿真列表失败:', error);
    res.status(500).json({ 
      error: '服务器内部错误',
      message: error.message 
    });
  }
});

// 运行并网仿真
app.post('/api/simulation/run', (req, res) => {
  try {
    console.log('收到并网仿真请求...');

    // 可以接收自定义配置参数
    const customConfig = req.body.config || {};
    const mergedConfig = { ...CONFIG, ...customConfig };

    console.log('开始运行并网仿真...');
    const results = runSimulation(mergedConfig);

    // 保存结果并获取文件ID
    const fileId = saveResults(results, mergedConfig);

    res.json({
      success: true,
      message: '并网仿真运行完成',
      fileId: fileId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('运行并网仿真失败:', error);
    res.status(500).json({
      error: '并网仿真运行失败',
      message: error.message
    });
  }
});

// 运行离网仿真
app.post('/api/simulation/offgrid/run', (req, res) => {
  try {
    console.log('收到离网仿真请求...');

    // 可以接收自定义配置参数
    const customConfig = req.body.config || {};
    const mergedConfig = { ...CONFIG_OFFGRID, ...customConfig };

    console.log('开始运行离网仿真...');
    const results = runOffgridSimulation(mergedConfig);

    // 保存结果并获取文件ID
    const fileId = saveOffgridResults(results, mergedConfig);

    res.json({
      success: true,
      message: '离网仿真运行完成',
      fileId: fileId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('运行离网仿真失败:', error);
    res.status(500).json({
      error: '离网仿真运行失败',
      message: error.message
    });
  }
});

// 获取离网仿真结果
app.get('/api/simulation/offgrid/:id', (req, res) => {
  try {
    const id = req.params.id;
    const filePath = path.join(__dirname, 'off_grid', `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: '离网仿真结果不存在',
        message: `找不到ID为 ${id} 的离网仿真结果文件`
      });
    }

    const data = fs.readFileSync(filePath, 'utf8');
    const result = JSON.parse(data);

    res.json(result);
  } catch (error) {
    console.error('获取离网仿真结果失败:', error);
    res.status(500).json({
      error: '服务器内部错误',
      message: error.message
    });
  }
});

// 获取所有离网仿真结果列表
app.get('/api/simulations/offgrid', (req, res) => {
  try {
    const outputDir = path.join(__dirname, 'off_grid');

    // 确保目录存在
    if (!fs.existsSync(outputDir)) {
      return res.json([]);
    }

    const files = fs.readdirSync(outputDir)
      .filter(file => file.endsWith('.json'))
      .map(file => {
        try {
          const filePath = path.join(outputDir, file);
          const data = fs.readFileSync(filePath, 'utf8');
          const result = JSON.parse(data);

          return {
            id: result.id,
            timestamp: result.timestamp,
            mode: result.mode || 'offgrid',
            hydrogenProduction: result.statistics?.hydrogenProduction || 0,
            averageStorageSoc: result.statistics?.averageStorageSoc || 0,
            totalRenewableEnergy: result.statistics?.totalRenewableEnergy || 0
          };
        } catch (error) {
          console.error(`解析离网仿真文件 ${file} 失败:`, error);
          return null;
        }
      })
      .filter(item => item !== null)
      .sort((a, b) => b.id - a.id); // 按ID降序排列

    res.json(files);
  } catch (error) {
    console.error('获取离网仿真列表失败:', error);
    res.status(500).json({
      error: '服务器内部错误',
      message: error.message
    });
  }
});

// 获取默认并网配置
app.get('/api/config', (req, res) => {
  console.log('CONFIG.electrolyzers:', JSON.stringify(CONFIG.electrolyzers, null, 2));
  res.json(CONFIG);
});

// 获取默认离网配置
app.get('/api/config/offgrid', (req, res) => {
  res.json(CONFIG_OFFGRID);
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('未处理的错误:', error);
  res.status(500).json({
    error: '服务器内部错误',
    message: error.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    message: `找不到路径: ${req.path}`
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`=================================`);
  console.log(`风光储网制氢仿真API服务器已启动`);
  console.log(`端口: ${PORT}`);
  console.log(`时间: ${new Date().toLocaleString()}`);
  console.log(`=================================`);
  console.log(`API端点:`);
  console.log(`  GET  /api/health              - 健康检查`);
  console.log(`  GET  /api/config              - 获取默认配置`);
  console.log(`  GET  /api/simulations         - 获取仿真列表`);
  console.log(`  GET  /api/simulation/:id      - 获取仿真结果`);
  console.log(`  POST /api/simulation/run      - 运行新仿真`);
  console.log(`=================================`);
});

module.exports = app;

{"name": "device-api-frontend", "version": "1.0.0", "description": "Wind-Solar-Storage-Grid-Hydrogen Frontend", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 8080"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "echarts": "^5.4.3", "element-plus": "^2.3.14", "vue": "^3.3.4", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9"}, "type": "module"}
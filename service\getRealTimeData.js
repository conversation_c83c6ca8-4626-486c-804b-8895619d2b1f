import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'



/**
 * 3.2 获取实时数据.查询储能最新的电压、电流、有功功率、储能的状态
 */
export async function getDeviceRealTimeData(params) {
  const rtParams = [
    {
      "model": "pub_energy_consumer", // 电解槽
      "device_list": [
        {
          "device_id": 1, // 5-ALK_15_1,
          "col_list": ["DJCYT7"], // TODO: 只能获取遥信，遥测？传遥调数据-如开关机，获取状态？
        }
      ]
    },
    {
      "model": "pub_energy_storage", // 储能
      "device_list": [
        {
          "device_id": 1, // 1-储能1,2-储能2
          "col_list": ["soc", "soh"], // TODO:储能当前 SOC，SOH ?
        }
      ]
    }
  ]
  const { code, data, msg } = await getRealTimeData(rtParams)
  if (code == 0) {
      console.log('Get RealTime Data:', JSON.stringify(data, null, 2 ))
  } else {
    console.error('ERR:', msg)
  }
}

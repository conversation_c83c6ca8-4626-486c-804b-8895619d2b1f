import { createRouter, createWebHashHistory } from 'vue-router'
import GridConnected from '../views/GridConnected.vue'
import OffGrid from '../views/OffGrid.vue'

const routes = [
  {
    path: '/',
    redirect: '/grid-connected'
  },
  {
    path: '/grid-connected',
    name: 'GridConnected',
    component: GridConnected,
    meta: {
      title: '并网运行策略'
    }
  },
  {
    path: '/off-grid',
    name: 'OffGrid',
    component: OffGrid,
    meta: {
      title: '离网运行策略'
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - 风光储网制氢仿真系统`
  }
  next()
})

export default router

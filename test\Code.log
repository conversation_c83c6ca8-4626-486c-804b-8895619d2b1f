[Running] node "e:\02 ems code\device-api\src\setAdjust.js"
ALK_15_1 CLOSE: undefined

[<PERSON>] exited with code=0 in 22.859 seconds

[Running] node "e:\02 ems code\device-api\service\getDeviceAndCol.js"

[Done] exited with code=0 in 2.028 seconds

[Running] node "e:\02 ems code\device-api\service\getDeviceAndCol.js"
Get Device and Col Data: [
  {
    "model": "pub_energy_storage",
    "model_name": "储能",
    "device_list": [
      {
        "device_id": 1,
        "device_name": "储能1",
        "analogs": [
          {
            "name": "",
            "col": "u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "soc",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "soh",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_32",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "dcyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx33",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "dcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt1",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 2,
        "device_name": "储能2",
        "analogs": [
          {
            "name": "",
            "col": "u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "soc",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "soh",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "bms_32",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "dcyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyx33",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "dcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dcyt1",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_solar_generator",
    "model_name": "光伏",
    "device_list": [
      {
        "device_id": 1,
        "device_name": "光伏模拟源1",
        "analogs": [
          {
            "name": "",
            "col": "pvyc1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "GF",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "on",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "off",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx34",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 2,
        "device_name": "光伏模拟源2",
        "analogs": [
          {
            "name": "",
            "col": "pvyc1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyc44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyt11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "GF",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "on",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "off",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pvyx34",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_energy_consumer",
    "model_name": "负荷",
    "device_list": [
      {
        "device_id": 7,
        "device_name": "10kW电解槽1",
        "analogs": [
          {
            "name": "",
            "col": "DJC1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC30",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "fzyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx48",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "DJCYT7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "set_p",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 8,
        "device_name": "10kW电解槽2",
        "analogs": [
          {
            "name": "",
            "col": "DJC1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC30",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "fzyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx48",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "DJCYT7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "set_p",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 5,
        "device_name": "15kW电解槽1",
        "analogs": [
          {
            "name": "",
            "col": "DJC1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC30",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "fzyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx48",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "DJCYT7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "set_p",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 6,
        "device_name": "15kW电解槽2",
        "analogs": [
          {
            "name": "",
            "col": "DJC1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJC30",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "fzyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "fzyx48",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "DJCYT7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "DJCYT2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "set_p",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_breaker",
    "model_name": "开关",
    "device_list": [
      {
        "device_id": 8,
        "device_name": "15kW直流源2",
        "analogs": [],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "djcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt7",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 1,
        "device_name": "安科瑞电表",
        "analogs": [
          {
            "name": "",
            "col": "bw_num",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "ua",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ub",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "uc",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ia",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ib",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ic",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "q",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "cos",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "u",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 2,
        "device_name": "电网模拟器",
        "analogs": [
          {
            "name": "",
            "col": "bw_num",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "ua",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ub",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "uc",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ia",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ib",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "ic",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "q",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "cos",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "p",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 9,
        "device_name": "10kW直流源1",
        "analogs": [],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "djcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt7",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 10,
        "device_name": "10kW直流源2",
        "analogs": [],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "djcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt7",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      },
      {
        "device_id": 7,
        "device_name": "15kW直流源1",
        "analogs": [],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [
          {
            "name": "",
            "col": "djcyt2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "djcyt7",
            "valid": 1,
            "values": {}
          }
        ],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_wind_generator",
    "model_name": "风机",
    "device_list": [
      {
        "device_id": 1,
        "device_name": "风机1",
        "analogs": [
          {
            "name": "",
            "col": "xfj1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj39",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 2,
        "device_name": "风机2",
        "analogs": [
          {
            "name": "",
            "col": "xfj1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "xfj39",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_acdc",
    "model_name": "ACDC",
    "device_list": [
      {
        "device_id": 2,
        "device_name": "储能pcs2",
        "analogs": [
          {
            "name": "",
            "col": "pcs1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs45",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs46",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs47",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs48",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs49",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs50",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs51",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs52",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs53",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs54",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs55",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs56",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs57",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs58",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs59",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs60",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs61",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs62",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs63",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs64",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs65",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs66",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs67",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs68",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs69",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs70",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs71",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs72",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs73",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs74",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs75",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs76",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs77",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs78",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs79",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs80",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs81",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs82",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs83",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs84",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs85",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs86",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs87",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs88",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs89",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs90",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs91",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs92",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs93",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs94",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs95",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs96",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs97",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs98",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs99",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs100",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs101",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs102",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs103",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs104",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs105",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs106",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs107",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs108",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs109",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs110",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs111",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs112",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs113",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs114",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs115",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs116",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs117",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs118",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs119",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs120",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs121",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs122",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs123",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs124",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs125",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs126",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs127",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs128",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs129",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs130",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs131",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs132",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs133",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs134",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs135",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs136",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs137",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs138",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs139",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs140",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs141",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs142",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs143",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs144",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs145",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs146",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs147",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs148",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs149",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_p",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc12",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc13",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc14",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc15",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc16",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc17",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc18",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc19",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyt1",
            "valid": 0,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 3,
        "device_name": "华为逆变器1",
        "analogs": [
          {
            "name": "",
            "col": "pcs1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs12",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs13",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs14",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs15",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs16",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs17",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs18",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs19",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs30",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs31",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs32",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs33",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs34",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs35",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs36",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs37",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs38",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs39",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs40",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs41",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs42",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs43",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs44",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs45",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs46",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs47",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs48",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs49",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs50",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs51",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs52",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs53",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs54",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs55",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs56",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs57",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs58",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs59",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs60",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs61",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs62",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs63",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs64",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs65",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs66",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs67",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs68",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs69",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs70",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs71",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs72",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs73",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs74",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs75",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs76",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs77",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs78",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs79",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs80",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs81",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs82",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs83",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs84",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs85",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs86",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs87",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs88",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs89",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs90",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs91",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs92",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs93",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs94",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs95",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs96",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs97",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs98",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs99",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs100",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs101",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs102",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs103",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs104",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs105",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs106",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs107",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs108",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs109",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs110",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs111",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs112",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs113",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs114",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs115",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs116",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs117",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs118",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs119",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs120",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs121",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs122",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs123",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs124",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs125",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs126",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs127",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs128",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs129",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs130",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs131",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs132",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs133",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs134",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs135",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs136",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs137",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs138",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs139",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs140",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs141",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs142",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs143",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs144",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs145",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs146",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs147",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs148",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs149",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_u",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_i",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_p",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyt1",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "acdcyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx45",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx46",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx47",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx48",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx49",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx50",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx51",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx52",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx53",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx54",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx55",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx56",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx57",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx58",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx59",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx60",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx61",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx62",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx63",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx64",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx65",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx66",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx67",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx68",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx69",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx70",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx71",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx72",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx73",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx74",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx75",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx76",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx77",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx78",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx79",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx80",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx81",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx82",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx83",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 4,
        "device_name": "华为逆变器2",
        "analogs": [
          {
            "name": "",
            "col": "pcs1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs12",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs13",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs14",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs15",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs16",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs17",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs18",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs19",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs30",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs31",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs32",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs33",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs34",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs35",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs36",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs37",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs38",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs39",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs40",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs41",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs42",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs43",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs44",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs45",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs46",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs47",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs48",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs49",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs50",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs51",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs52",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs53",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs54",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs55",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs56",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs57",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs58",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs59",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs60",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs61",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs62",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs63",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs64",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs65",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs66",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs67",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs68",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs69",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs70",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs71",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs72",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs73",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs74",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs75",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs76",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs77",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs78",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs79",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs80",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs81",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs82",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs83",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs84",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs85",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs86",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs87",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs88",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs89",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs90",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs91",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs92",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs93",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs94",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs95",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs96",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs97",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs98",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs99",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs100",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs101",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs102",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs103",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs104",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs105",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs106",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs107",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs108",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs109",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs110",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs111",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs112",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs113",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs114",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs115",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs116",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs117",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs118",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs119",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs120",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs121",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs122",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs123",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs124",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs125",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs126",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs127",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs128",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs129",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs130",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs131",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs132",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs133",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs134",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs135",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs136",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs137",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs138",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs139",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs140",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs141",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs142",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs143",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs144",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs145",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs146",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs147",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs148",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs149",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_u",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_i",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_p",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyt1",
            "valid": 1,
            "values": {}
          }
        ],
        "discrete": [
          {
            "name": "",
            "col": "acdcyx1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx7",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx9",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx20",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx21",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx22",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx23",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx24",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx25",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx26",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx27",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx28",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx29",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx45",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx46",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx47",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx48",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx49",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx50",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx51",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx52",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx53",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx54",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx55",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx56",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx57",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx58",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx59",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx60",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx61",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx62",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx63",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx64",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx65",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx66",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx67",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx68",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx69",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx70",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx71",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx72",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx73",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx74",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx75",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx76",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx77",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx78",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx79",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx80",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx81",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx82",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyx83",
            "valid": 1,
            "values": {}
          }
        ],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 1,
        "device_name": "储能pcs1",
        "analogs": [
          {
            "name": "",
            "col": "pcs1",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs2",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs3",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs4",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs5",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs6",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs8",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs10",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs11",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs12",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs13",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs14",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs15",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs16",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs17",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs18",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs19",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs30",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs31",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs32",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs33",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs34",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs35",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs36",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs37",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs38",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs39",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs40",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs41",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs42",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs43",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs44",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs45",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs46",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs47",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs48",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs49",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs50",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs51",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs52",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs53",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs54",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs55",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs56",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs57",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs58",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs59",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs60",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs61",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs62",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs63",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs64",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs65",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs66",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs67",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs68",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs69",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs70",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs71",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs72",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs73",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs74",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs75",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs76",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs77",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs78",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs79",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs80",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs81",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs82",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs83",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs84",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs85",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs86",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs87",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs88",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs89",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs90",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs91",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs92",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs93",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs94",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs95",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs96",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs97",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs98",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs99",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs100",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs101",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs102",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs103",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs104",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs105",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs106",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs107",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs108",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs109",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs110",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs111",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs112",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs113",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs114",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs115",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs116",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs117",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs118",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs119",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs120",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs121",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs122",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs123",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs124",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs125",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs126",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs127",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs128",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs129",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs130",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs131",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs132",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs133",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs134",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs135",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs136",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs137",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs138",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs139",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs140",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs141",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs142",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs143",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs144",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs145",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs146",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs147",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs148",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "pcs149",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_u",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_i",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "dc_p",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc1",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc2",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc3",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc4",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc5",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc6",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc7",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc8",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc9",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc10",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc11",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc12",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc13",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc14",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc15",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc16",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc17",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc18",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc19",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc20",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc21",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc22",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc23",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc24",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc25",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc26",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc27",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc28",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdc29",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "acdcyt1",
            "valid": 0,
            "values": {}
          }
        ],
        "discrete": [],
        "accumulator": [],
        "control": [],
        "teleset": [],
        "curveset": []
      }
    ]
  },
  {
    "model": "pub_station",
    "model_name": "站点",
    "device_list": [
      {
        "device_id": 11,
        "device_name": "隆基微电网实验室储能曲线",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 2,
        "device_name": "隆基微电网实验室光伏模拟器1",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 3,
        "device_name": "隆基微电网实验室光伏模拟器2",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 4,
        "device_name": "隆基微电网实验室风机1",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 7,
        "device_name": "隆基微电网实验室风机2",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 1,
        "device_name": "隆基微电网实验室",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 1,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": []
      },
      {
        "device_id": 15,
        "device_name": "隆基微电网实验室10kw电解槽1",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 16,
        "device_name": "隆基微电网实验室10kw电解槽2",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 13,
        "device_name": "隆基微电网实验室15kw电解槽1",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      },
      {
        "device_id": 14,
        "device_name": "隆基微电网实验室15kw电解槽2",
        "analogs": [],
        "discrete": [],
        "accumulator": [
          {
            "name": "",
            "col": "djc_total",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "green_rate",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "wind_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_discharge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "storage_charge_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "solar_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "out_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "in_grid_kwh",
            "valid": 0,
            "values": {}
          },
          {
            "name": "",
            "col": "profit",
            "valid": 0,
            "values": {}
          }
        ],
        "control": [],
        "teleset": [],
        "curveset": [
          {
            "name": "",
            "col": "storage_p",
            "valid": 1,
            "values": {}
          }
        ]
      }
    ]
  }
]

[Done] exited with code=0 in 2.757 seconds


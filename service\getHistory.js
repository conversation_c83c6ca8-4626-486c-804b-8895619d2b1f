import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'


/**
 * 3 查询历史数据
 */
export async function getDeviceHistoryData(params) {
  const hisParams = {
    model_list: [
      {
        "model": "pub_energy_consumer", // 电解槽
        "device_list": [
          {
            "device_id": 1, // 5-ALK_15_1,
            "col_list": ["DJCYT7"], // TODO: 只能获取遥信，遥测？传遥调数据-如开关机，获取状态？
          }
        ]
      },
      {
        "model": "pub_energy_storage", // 储能
        "device_list": [
          {
            "device_id": 1, // 1-储能1,2-储能2
            "col_list": ["soc", "soh"], // TODO:储能当前 SOC，SOH ?
          }
        ]
      }
    ],
    start_time: "2022-04-01 00:00:00", // TODO: YYYY-MM-DD HH:mm:ss 格式，不是秒级时间戳？
    end_time: "2022-04-01 00:20:00"
  }
  const { code, data, msg } = await getHistoryData(hisParams)
  if (code === 0) {
    console.log('Get History Data:', JSON.stringify(data, null, 2))
  } else {
    console.error('ERR:', msg)
  }
}
### set curve
POST http://10.58.0.15:32435/api/v3/mgec/extra/set_curve HTTP/1.1
content-type: application/json
accessSecret: 21ED2C5B509F49FF8E323C67B9A2B084

{
  "model": "pub_station",
  "device_id": 3,
  "col": "storage_p",
  "curve": [
    {
      "timestamp": 1754666700,
      "value": 13.17
    },
    {
      "timestamp": 1754667600,
      "value": 12.2325
    },
    {
      "timestamp": 1754668500,
      "value": 11.4225
    },
    {
      "timestamp": 1754669400,
      "value": 14.134500000000001
    },
    {
      "timestamp": 1754670300,
      "value": 15
    },
    {
      "timestamp": 1754671200,
      "value": 12.1965
    },
    {
      "timestamp": 1754672100,
      "value": 15
    },
    {
      "timestamp": 1754673000,
      "value": 15
    },
    {
      "timestamp": 1754673900,
      "value": 11.8245
    },
    {
      "timestamp": 1754674800,
      "value": 15
    },
    {
      "timestamp": 1754675700,
      "value": 12.9255
    },
    {
      "timestamp": 1754676600,
      "value": 12.7065
    },
    {
      "timestamp": 1754677500,
      "value": 10.3005
    },
    {
      "timestamp": 1754678400,
      "value": 13.416
    },
    {
      "timestamp": 1754679300,
      "value": 7.755
    },
    {
      "timestamp": 1754680200,
      "value": 7.821
    },
    {
      "timestamp": 1754681100,
      "value": 5.3115000000000006
    },
    {
      "timestamp": 1754682000,
      "value": 7.597499999999999
    },
    {
      "timestamp": 1754682900,
      "value": 4.449
    },
    {
      "timestamp": 1754683800,
      "value": 5.673
    },
    {
      "timestamp": 1754684700,
      "value": 4.4805
    },
    {
      "timestamp": 1754685600,
      "value": 5.802
    },
    {
      "timestamp": 1754686500,
      "value": 6.258
    },
    {
      "timestamp": 1754687400,
      "value": 6.27
    },
    {
      "timestamp": 1754688300,
      "value": 4.4595
    },
    {
      "timestamp": 1754689200,
      "value": 6.3165
    },
    {
      "timestamp": 1754690100,
      "value": 5.865
    },
    {
      "timestamp": 1754691000,
      "value": 5.7015
    },
    {
      "timestamp": 1754691900,
      "value": 4.56
    },
    {
      "timestamp": 1754692800,
      "value": 4.413
    },
    {
      "timestamp": 1754693700,
      "value": 3.9345
    },
    {
      "timestamp": 1754694600,
      "value": 6.1080000000000005
    },
    {
      "timestamp": 1754695500,
      "value": 3.1035
    },
    {
      "timestamp": 1754696400,
      "value": 2.736
    },
    {
      "timestamp": 1754697300,
      "value": 2.0564999999999998
    },
    {
      "timestamp": 1754698200,
      "value": 1.1025
    },
    {
      "timestamp": 1754699100,
      "value": 0
    },
    {
      "timestamp": 1754700000,
      "value": 0
    },
    {
      "timestamp": 1754700900,
      "value": 0
    },
    {
      "timestamp": 1754701800,
      "value": 0
    },
    {
      "timestamp": 1754702700,
      "value": 0
    },
    {
      "timestamp": 1754703600,
      "value": 0
    },
    {
      "timestamp": 1754704500,
      "value": 0
    },
    {
      "timestamp": 1754705400,
      "value": 0
    },
    {
      "timestamp": 1754706300,
      "value": 0
    },
    {
      "timestamp": 1754707200,
      "value": 0
    },
    {
      "timestamp": 1754708100,
      "value": 0
    },
    {
      "timestamp": 1754709000,
      "value": 0
    },
    {
      "timestamp": 1754709900,
      "value": 0
    },
    {
      "timestamp": 1754710800,
      "value": 0
    },
    {
      "timestamp": 1754711700,
      "value": 0
    },
    {
      "timestamp": 1754712600,
      "value": 0
    },
    {
      "timestamp": 1754713500,
      "value": 0
    },
    {
      "timestamp": 1754714400,
      "value": 0
    },
    {
      "timestamp": 1754715300,
      "value": 0
    },
    {
      "timestamp": 1754716200,
      "value": 0
    },
    {
      "timestamp": 1754717100,
      "value": 0
    },
    {
      "timestamp": 1754718000,
      "value": 0
    },
    {
      "timestamp": 1754718900,
      "value": 0
    },
    {
      "timestamp": 1754719800,
      "value": 0
    }
  ]
}

### set soc
POST http://10.58.0.15:32435/api/v3/mgec/extra/teleset HTTP/1.1
content-type: application/json
accessSecret: 21ED2C5B509F49FF8E323C67B9A2B084

{
  "model": "pub_energy_storage",
  "device_id": 2,
  "col": "dcyt22",
  "value": 50
}
import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'


// 3.5 光伏预测 TODO: ？
// 3.6 负荷预测 TODO: ？

/**
 * 3.7 遥控命令下发
 */
export async function setDeviceControl(params) {
  const controlParams = {
    "model": "pub_breaker",
    "device_id": 1,
    "col": "on", // TODO: 所有遥控值？excel 表中无遥控？
    "value": 1, // 0-无效，1-有效 TODO: 意义
    "type": 1 // 1-标准遥控，2-直接遥控 TODO: 意义
  }
  const { code, data, msg } = await setControl(controlParams)
  if (code === 0) {
    console.log('下发遥控：', JSON.stringify(data, null, 2))
  } else {
    console.error('ERR:', msg)
  }
}
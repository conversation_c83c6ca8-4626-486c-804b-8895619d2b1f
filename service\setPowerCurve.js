import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'

/**
 * 9. 曲线下发，主要功率下发
 * TODO:Q: 下发的功率时间间隔最小 5 min？时间戳单位是 s？充放电如何两向控制？
 * TODO: pub_energy_consumer，device_id:5（电解槽1）, DJCYT4(设定功率)，工作模式(0:直流电压源    2:直流负载 3:电解槽),
 * 冷启动时间的设置；尤其功率也能设置(先设置直流源？)？
 */
export async function setDevicePowerCurve(deviceName, powerCurve) {
  const deviceNameList = {
    PV_1: { device_id: 2 },
    PV_2: { device_id: 3 },
    WIND_1: { device_id: 4 },
    WIND_2: { device_id: 7 },
    BAT_1: { device_id: 11 }, 
    ALK_15_1: { device_id: 13 },
    ALK_15_2: { device_id: 14 },
    ALK_10_1: { device_id: 15 },
    ALK_10_2: { device_id: 16 }, 

  }
  const batParams = {
    "model": "pub_station",
    "device_id": deviceNameList[deviceName].device_id,
    "col": "storage_p",
    "curve": powerCurve 
  }
  // console.log('batParams:', batParams)
  const { code, msg, ...data } = await setPowerCurve(batParams)
  // const { code, data, msg } = { code: 0, msg: `Set ${deviceName} Power Succeed:`, data: batParams}
  if (code ===0 ) {
    console.log(`Set ${deviceName} Power Succeed:`, msg)
  } else {
    console.log(`Set ${deviceName} Power Fail:`, msg)
  }
}


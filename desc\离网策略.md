# 并网下控制策略
## 1. 各设备限制条件
### 风机
- 容量：30kw [可配置]
- 预测曲线(生成曲线)，可先运行脚本生成高仿模拟数据, 可在方法里传参指定生成多1~N天数据 [可配置]

### 光伏
- 容量：30kw [可配置]
- 预测曲线(生成曲线)，可先运行脚本生成高仿模拟数据，可在方法里传参指定生成多1~N天数据 [可配置]

### 储能
- 容量：15kwh [可配置]
- 初始soc：80% [可配置]
- soc上下限：10%~90% [可配置]
- 电倍率：1，即最大充放电功率 15kw

### 电解槽
- 4台电解槽：15kw 2台，10kw 2台 [可配置]
- 功率范围：30%~100% 
- 能耗：4.3 kwh/Nm³ [可配置]

- 注意：
  - 初始状态为关机或开机，可手动配参调整

## 2. 控制策略
风光储网制氢-离网策略验证：
- 基于预设的风光功率曲线
- 绿电功率充足(均分后所有电解槽功率 >= 30%)，电解槽(最大程度)等比例均分功率, 有剩余的绿电，则储能消纳
- 绿电功率不足(均分后所有电解槽功率 < 30%), 则储能供电，所有电解槽以30% 的功率运行；如果功率还不够则热备最小数量的电解槽，小功率的先热备，剩余的功率等比例均分正常运行；如果还是不够，则停机最小数量的电解槽，小功率的先停机，剩余的功率等比例均分正常运行。等绿电恢复充足时，重新开始功率均分。
- 电解槽运行状态
  - 正常运行：30% <= 功率 <= 100%
  - 热备：10% <= 功率 <30% 功率之间, 需要热备的话，保持10% 功率即可
  - 停机：功率 < 10%，立刻停机，功率 为0，再次启动需要冷启动,冷启动时间：30min
  - 状态切换：
    - 关机 -> 正常运行，功率升至 30% 及以上，有 30min 功率爬坡,其余时间功率调整时间忽略不计。0~30% 功率这个过程称为冷启动，期间产氢量不计。正常运行期间
    - 正常 -> 热备，功率降至10%，10%的功率状态成为热备， 热备期间，产氢量不计
    - 热备 -> 关机，功率降至0，时间忽略不计


## 3. 项目要求
- 后端
  - 基于 nodejs 和 express
  - 可以运行脚本基于第2项控制策略生成风光出力曲线和分时电价测试数据
  - 可以运行脚本生成如下 json 数据：
    - 风光储网电解槽运行曲线数据，包含风机，光伏，储能，电解槽未来一段时间(根据生成的风光出力曲线时间跨度)的运行曲线数据，注意这些折线都在一个图中。
      - 注意，默认曲线为 曲线包含风光一体(即风机+光伏的合体)，电解槽一体化(4台电解槽相加的)，储能，电网，储能 soc。风光，4台电解槽单独的曲线默认关闭标签，也可手动打开。整体时间轴可拖住查看整体及局部。
    - 产氢量(单位：kg)，产氢量=制氢的电量/对应电解槽电耗，上下网电量(kwh)及占比(上下网电量/新能源发电量)
- 前端
  - 前端基于 vue3 和 echarts，可以通过 http api 接口基于 id 获取后端生成的曲线数据

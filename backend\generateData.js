const fs = require('fs');
const path = require('path');
const START_TIME = '2025/8/15 10:00:00'
const DAY = 1
const TIME_STEP = 15

// ========== 数据生成脚本 ==========
// 用于生成风光出力曲线和分时电价测试数据

/**
 * 生成风机功率预测曲线（标幺值）
 * @param {number} days - 生成天数
 * @param {number} timeStep - 时间步长（分钟）
 * @returns {Array} 标幺值数组 (0-1)
 */
function generateWindPowerCurve(days = 1, timeStep = 15) {
  const totalPoints = (days * 24 * 60) / timeStep;
  const windCurve = [];
  
  for (let i = 0; i < totalPoints; i++) {
    const hour = (i * timeStep / 60) % 24;
    
    // 模拟风力日变化规律：夜间风力较强，白天较弱
    let baseWind = 0.6 + 0.3 * Math.sin((hour - 6) / 24 * 2 * Math.PI);
    
    // 添加随机波动
    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2倍波动
    
    // 添加长周期变化
    const longPeriodFactor = 0.9 + 0.2 * Math.sin(i / totalPoints * 4 * Math.PI);
    
    let windPower = baseWind * randomFactor * longPeriodFactor;
    
    // 限制在0-1范围内
    windPower = Math.max(0, Math.min(1, windPower));
    
    windCurve.push(Number(windPower.toFixed(4)));
  }
  
  return windCurve;
}

/**
 * 生成光伏功率预测曲线（标幺值）
 * @param {number} days - 生成天数
 * @param {number} timeStep - 时间步长（分钟）
 * @returns {Array} 标幺值数组 (0-1)
 */
function generateSolarPowerCurve(days = 1, timeStep = 15) {
  const totalPoints = (days * 24 * 60) / timeStep;
  const solarCurve = [];
  
  for (let i = 0; i < totalPoints; i++) {
    const hour = (i * timeStep / 60) % 24;
    let solarPower = 0;
    
    // 光伏只在白天6:00-18:00有出力
    if (hour >= 6 && hour <= 18) {
      // 使用正弦函数模拟太阳辐射变化
      const solarAngle = Math.sin(((hour - 6) / 12) * Math.PI);
      
      // 基础出力曲线
      let baseSolar = solarAngle;
      
      // 添加云层遮挡等随机因素
      const cloudFactor = 0.7 + Math.random() * 0.6; // 0.7-1.3倍波动
      
      // 添加天气变化
      const weatherFactor = 0.8 + 0.4 * Math.sin(i / totalPoints * 6 * Math.PI);
      
      solarPower = baseSolar * cloudFactor * weatherFactor;
    }
    
    // 限制在0-1范围内
    solarPower = Math.max(0, Math.min(1, solarPower));
    
    solarCurve.push(Number(solarPower.toFixed(4)));
  }
  
  return solarCurve;
}

/**
 * 生成分时电价数据
 * @param {number} days - 生成天数
 * @param {number} timeStep - 时间步长（分钟）
 * @returns {Array} 电价数组 (元/kWh)
 */
function generateElectricityPrice(days = 1, timeStep = 15) {
  const totalPoints = (days * 24 * 60) / timeStep;
  const priceData = [];
  
  for (let i = 0; i < totalPoints; i++) {
    const hour = (i * timeStep / 60) % 24;
    let price = 0;
    
    // 分时电价策略
    if (hour >= 8 && hour < 11 || hour >= 18 && hour < 23) {
      // 高峰时段：8-11点，18-23点
      price = 0.8 + Math.random() * 0.2; // 0.8-1.0元/kWh
    } else if (hour >= 11 && hour < 18) {
      // 平时段：11-18点
      price = 0.5 + Math.random() * 0.2; // 0.5-0.7元/kWh
    } else {
      // 谷时段：23-8点
      price = 0.2 + Math.random() * 0.2; // 0.2-0.4元/kWh
    }
    
    priceData.push(Number(price.toFixed(4)));
  }
  
  return priceData;
}

/**
 * 生成时间戳数组
 * @param {number} days - 生成天数
 * @param {number} timeStep - 时间步长（分钟）
 * @param {string} startTime - 开始时间
 * @returns {Array} 时间戳数组
 */
function generateTimestamps(days = 1, timeStep = 15, startTime = START_TIME) {
  const totalPoints = (days * 24 * 60) / timeStep;
  const timestamps = [];
  const start = new Date(startTime);
  
  for (let i = 0; i < totalPoints; i++) {
    const timestamp = new Date(start.getTime() + i * timeStep * 60 * 1000);
    timestamps.push(timestamp.toLocaleString());
  }
  
  return timestamps;
}

/**
 * 保存生成的数据到文件
 * @param {Object} data - 要保存的数据
 * @param {string} filename - 文件名
 */
function saveDataToFile(data, filename) {
  const dataDir = path.join(__dirname, 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  const filePath = path.join(dataDir, filename);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`数据已保存到: ${filePath}`);
}

/**
 * 主函数：生成所有基础数据
 * @param {number} days - 生成天数
 * @param {number} timeStep - 时间步长（分钟）
 */
function generateAllData(days = DAY, timeStep = TIME_STEP) {
  console.log(`开始生成 ${days} 天的基础数据...`);
  console.log(`时间步长: ${timeStep} 分钟`);
  
  const startTime = START_TIME;
  
  // 生成时间戳
  const timestamps = generateTimestamps(days, timeStep, startTime);
  
  // 生成风机功率曲线（标幺值）
  const windPowerCurve = generateWindPowerCurve(days, timeStep);
  
  // 生成光伏功率曲线（标幺值）
  const solarPowerCurve = generateSolarPowerCurve(days, timeStep);
  
  // 生成分时电价
  const electricityPrice = generateElectricityPrice(days, timeStep);
  
  // 组装数据
  const data = {
    metadata: {
      days: days,
      timeStep: timeStep,
      startTime: startTime,
      totalPoints: timestamps.length,
      generatedAt: new Date().toISOString()
    },
    timestamps: timestamps,
    windPowerCurve: windPowerCurve,      // 标幺值 (0-1)
    solarPowerCurve: solarPowerCurve,    // 标幺值 (0-1)
    electricityPrice: electricityPrice    // 元/kWh
  };
  
  // 保存到文件
  const filename = `baseData_${days}days_${timeStep}min.json`;
  saveDataToFile(data, filename);
  
  // 输出统计信息
  console.log(`=== 数据生成完成 ===`);
  console.log(`时间点数: ${timestamps.length}`);
  console.log(`风机功率范围: ${Math.min(...windPowerCurve).toFixed(4)} - ${Math.max(...windPowerCurve).toFixed(4)}`);
  console.log(`光伏功率范围: ${Math.min(...solarPowerCurve).toFixed(4)} - ${Math.max(...solarPowerCurve).toFixed(4)}`);
  console.log(`电价范围: ${Math.min(...electricityPrice).toFixed(4)} - ${Math.max(...electricityPrice).toFixed(4)} 元/kWh`);
  
  return data;
}

/**
 * 读取基础数据文件
 * @param {number} days - 天数
 * @param {number} timeStep - 时间步长
 * @returns {Object} 基础数据对象
 */
function loadBaseData(days = DAY, timeStep = TIME_STEP) {
  const filename = `baseData_${days}days_${timeStep}min.json`;
  const filePath = path.join(__dirname, 'data', filename);
  
  if (!fs.existsSync(filePath)) {
    console.log(`基础数据文件不存在，正在生成: ${filename}`);
    return generateAllData(days, timeStep);
  }
  
  try {
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    console.log(`已加载基础数据: ${filename}`);
    return data;
  } catch (error) {
    console.error(`读取基础数据失败: ${error.message}`);
    console.log('重新生成基础数据...');
    return generateAllData(days, timeStep);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  
  generateAllData(DAY, TIME_STEP);
}

module.exports = {
  generateWindPowerCurve,
  generateSolarPowerCurve,
  generateElectricityPrice,
  generateTimestamps,
  generateAllData,
  loadBaseData
};

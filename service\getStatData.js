import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'


/**
 * 3.4获取(遥脉)统计数据, 发电量等数据
 */
export async function getDeviceStatData(params) {
  const statParams = {
    "model_list": [
      {
        "model": "pub_station",
        "device_list": [
          {
            "device_id": 1,
            "col_list": ["djc_total", "green_rate"], // TODO:电解槽制氢量，绿电利用率。只能是遥脉数据？
          }
        ],
      },
    ],
    "start_time": "2022-05-26",
    "end_time": "2022-05-31",
    "type": "total",
    "period": "day"
  }
  const { data, code, msg } = await getStatData(statParams)
  if (code === 0) {
    console.log('Get 遥脉Stat Data:', JSON.stringify(data, null, 2))
  } else {
    console.error('ERR:', msg)
  }
}

getDeviceStatData()
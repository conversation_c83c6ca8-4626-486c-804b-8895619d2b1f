<template>
  <div class="curve-chart">
    <div class="chart-header">
      <h3>风光储网制氢运行曲线</h3>
      <div class="chart-controls">
        <el-button
          @click="refreshData"
          :loading="loading"
          type="success"
          size="small"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 功率和SOC合并图表 -->
    <div class="chart-container">
      <div ref="powerChart" class="chart-item power-chart"></div>
    </div>
    
    <!-- 统计信息 -->
    <div class="statistics-panel" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic 
            title="产氢量" 
            :value="statistics.hydrogenProduction" 
            suffix="kg"
            :precision="2"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="可再生能源发电量" 
            :value="statistics.totalRenewableEnergy" 
            suffix="kWh"
            :precision="1"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="上下网电量占比" 
            :value="statistics.gridExchangeRatio" 
            suffix="%"
            :precision="1"
          />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="平均储能SOC" 
            :value="statistics.averageStorageSoc" 
            suffix="%"
            :precision="1"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { simulationApi } from '../api/simulation'
import { ElMessage } from 'element-plus'

const props = defineProps({
  simulationId: {
    type: [String, Number],
    default: null
  },
  mode: {
    type: String,
    default: 'grid' // 'grid' 或 'offgrid'
  }
})

// 响应式数据
const powerChart = ref(null)
const loading = ref(false)
const data = ref(null)
const statistics = ref(null)

// ECharts实例
let powerChartInstance = null

// 初始化图表
const initCharts = () => {
  if (powerChart.value) {
    powerChartInstance = echarts.init(powerChart.value)

    // 添加图表事件监听
    powerChartInstance.on('click', function (params) {
      console.log('图表点击事件:', params)
      if (params.componentType === 'series') {
        console.log(`点击了 ${params.seriesName} 系列`)
      }
    })

    powerChartInstance.on('legendselectchanged', function (params) {
      console.log('图例选择变化:', params)
    })

    powerChartInstance.on('dataZoom', function (params) {
      console.log('数据缩放事件:', params)
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  powerChartInstance?.resize()
}

// 获取数据
const fetchData = async (id) => {
  if (!id) return
  
  loading.value = true
  try {
    const response = await simulationApi.getSimulation(id)
    data.value = response.data
    statistics.value = response.data.statistics
    
    await nextTick()
    updateCharts()
    
    ElMessage.success('数据加载成功')
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  if (props.simulationId) {
    fetchData(props.simulationId)
  }
}

// 更新图表
const updateCharts = () => {
  if (!data.value) return

  updatePowerChart()
}

// 更新功率图表
const updatePowerChart = () => {
  if (!powerChartInstance || !data.value) return

  const { curves } = data.value
  const timestamps = curves.combined.timestamps.map(t => new Date(t))

  // 转换数据格式为 [timestamp, value] 格式
  const formatTimeSeriesData = (values) => {
    return values.map((value, index) => [timestamps[index], value])
  }

  // 构建双轴图表系列数据
  const series = [
    // 风光单独曲线（默认不选中）
    {
      name: '风机发电',
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(curves.individual.wind),
      smooth: true,
      lineStyle: { color: '#52C41A', width: 1 },
      symbol: 'none',
      sampling: 'lttb'
    },
    {
      name: '光伏发电',
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(curves.individual.solar),
      smooth: true,
      lineStyle: { color: '#95D475', width: 1 },
      symbol: 'none',
      sampling: 'lttb'
    },
    // 风光合并曲线（无面积图）
    {
      name: '风光发电',
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(curves.combined.windSolar),
      smooth: true,
      lineStyle: { color: '#389E0D', width: 2 },
      symbol: 'none',
      sampling: 'lttb'
    },
    // 电解槽分别显示（堆叠面积图）
    ...curves.individual.electrolyzers.map((e, index) => ({
      name: `电解槽${index + 1}`,
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(e.data),
      smooth: true,
      lineStyle: {
        color: ['#1890FF', '#722ED1', '#FA8C16', '#13C2C2'][index % 4],
        width: 1
      },
      areaStyle: {
        color: `rgba(${index === 0 ? '24, 144, 255' : index === 1 ? '114, 46, 209' : index === 2 ? '250, 140, 22' : '19, 194, 194'}, 0.6)`
      },
      stack: 'electrolyzer',
      symbol: 'none',
      sampling: 'lttb'
    })),
    {
      name: '储能功率',
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(curves.combined.storage),
      smooth: true,
      lineStyle: { color: '#FAAD14', width: 1 },
      symbol: 'none',
      sampling: 'lttb'
    },
    {
      name: '电网功率',
      type: 'line',
      yAxisIndex: 0,
      data: formatTimeSeriesData(curves.combined.grid),
      smooth: true,
      lineStyle: { color: '#F5222D', width: 2 },
      symbol: 'none',
      sampling: 'lttb'
    },
    // 储能SOC（右轴）
    {
      name: '储能SOC',
      type: 'line',
      yAxisIndex: 1,
      data: formatTimeSeriesData(curves.combined.storageSoc),
      smooth: true,
      lineStyle: { color: '#EB2F96', width: 2, type: 'dashed' },
      symbol: 'circle',
      symbolSize: 1,
      sampling: 'lttb'
    }
  ]
  
  const option = {
    title: {
      text: '功率曲线',
      left: 'center',
      textStyle: { fontSize: 16 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      formatter: function(params) {
        let result = `<div style="margin-bottom: 8px; font-weight: bold; border-bottom: 1px solid #eee; padding-bottom: 4px;">${params[0].axisValueLabel}</div>`
        params.forEach(param => {
          const unit = param.seriesName === '储能SOC' ? '%' : 'kW'
          let extraInfo = ''

          // 为电解槽添加负荷占比信息
          if (param.seriesName.includes('电解槽')) {
            const electrolyzerIndex = parseInt(param.seriesName.replace('电解槽', '')) - 1
            const capacity = electrolyzerIndex < 2 ? 15 : 10 // 前两台15kW，后两台10kW
            const loadRatio = param.value[1] > 0 ? ((param.value[1] / capacity) * 100).toFixed(1) : 0
            extraInfo = ` (负荷: ${loadRatio}%)`
          }

          result += `<div style="margin: 3px 0; display: flex; align-items: center;">
            <span style="display:inline-block;margin-right:8px;border-radius:50%;width:8px;height:8px;background-color:${param.color}"></span>
            <span style="min-width: 80px;">${param.seriesName}:</span>
            <span style="font-weight: bold; margin-left: 8px;">${param.value[1].toFixed(2)} ${unit}${extraInfo}</span>
          </div>`
        })
        return result
      }
    },
    legend: {
      top: 30,
      type: 'scroll',
      selected: {
        '风机发电': false,
        '光伏发电': false,
        '风光发电': true,
        '电解槽1': true,
        '电解槽2': true,
        '电解槽3': true,
        '电解槽4': true,
        '储能功率': true,
        '电网功率': true,
        '储能SOC': true
      },
      itemGap: 15,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '8%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        formatter: function(value) {
          const date = new Date(value)
          return `${date.getMonth()+1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
        },
        interval: 'auto',
        rotate: 45
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E4E7ED',
          type: 'dashed'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '功率 (kW)',
        position: 'left',
        axisLabel: {
          formatter: '{value} kW'
        }
      },
      {
        type: 'value',
        name: 'SOC (%)',
        position: 'right',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        },
        splitLine: {
          show: false
        }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none',
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false
      },
      {
        type: 'slider',
        xAxisIndex: 0,
        bottom: '3%',
        height: 25,
        handleSize: 20,
        handleStyle: {
          color: '#1890FF',
          borderColor: '#1890FF'
        },
        textStyle: {
          color: '#666'
        },
        borderColor: '#ddd',
        fillerColor: 'rgba(24, 144, 255, 0.2)',
        backgroundColor: '#f5f5f5'
      }
    ],
    series: series
  }
  
  powerChartInstance.setOption(option, true)
}





// 监听仿真ID变化
watch(() => props.simulationId, (newId) => {
  if (newId) {
    fetchData(newId)
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initCharts()
    if (props.simulationId) {
      fetchData(props.simulationId)
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  powerChartInstance?.dispose()
})
</script>

<style scoped>
.curve-chart {
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.chart-container {
  margin-bottom: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
}

.chart-item {
  width: 100%;
  background: white;
  min-width: 800px;
}

.power-chart {
  height: 500px;
}

.statistics-panel {
  background: #F5F7FA;
  padding: 20px;
  border-radius: 4px;
  margin-top: 20px;
}
</style>

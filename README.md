# 风光储网制氢仿真系统

基于并网策略的风光储网制氢系统仿真平台，包含后端仿真引擎和前端可视化界面。

## 项目结构

```
device-api/
├── backend/           # 后端仿真引擎
│   ├── simulation.js  # 主仿真脚本（包含所有配置和逻辑）
│   ├── app.js        # Express API服务器
│   ├── output/       # 仿真结果JSON文件
│   └── package.json
├── frontend/         # 前端可视化界面
│   ├── src/
│   │   ├── components/
│   │   │   └── CurveChart.vue    # 图表组件
│   │   ├── api/
│   │   │   └── simulation.js     # API调用
│   │   ├── App.vue              # 主应用
│   │   └── main.js
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 功能特性

### 设备模型
- **风机**: 30kW容量，基于风速的功率曲线
- **光伏**: 30kW容量，基于太阳辐射的功率曲线
- **储能**: 30kWh容量，SOC范围10%-90%，充放电功率10kW
- **电网**: 上下网功率限制20kW
- **电解槽**: 4台(15kW×2 + 10kW×1 + 5kW×1)，功率范围30%-100%

### 控制策略
- **绿电优先制氢**: 风光发电优先供给电解槽
- **功率分配**: 绿电充足时等比例分配，不足时优先保证大容量设备
- **状态管理**: 正常运行(30%-100%) → 热备(10%固定) → 停机(0%)
- **冷启动**: 30分钟功率爬坡过程

### 前端界面
- **双图表显示**: 功率曲线图 + SOC曲线图
- **视图切换**: 合并视图 / 详细视图
- **交互功能**: 时间轴拖拽、局部放大
- **统计信息**: 产氢量、上下网电量占比等

## 快速开始

### 1. 安装依赖

**后端:**
```bash
cd backend
npm install
```

**前端:**
```bash
cd frontend
npm install
```

### 2. 运行仿真

**方式一: 直接运行仿真脚本**
```bash
cd backend
node simulation.js
```

**方式二: 启动API服务器**
```bash
cd backend
npm start
# 或开发模式
npm run dev
```

### 3. 启动前端界面

```bash
cd frontend
npm run dev
```

访问: http://localhost:8080

## 配置参数

在 `backend/simulation.js` 文件顶部的 `CONFIG` 对象中可以修改所有设备参数：

```javascript
const CONFIG = {
  // 仿真时间配置
  simulation: {
    days: 1,                    // 仿真天数
    timeStep: 15,               // 时间步长(分钟)
  },
  
  // 风机配置
  windTurbine: {
    capacity: 30,               // kW 额定容量
  },
  
  // 光伏配置  
  solarPanel: {
    capacity: 30,               // kW 额定容量
  },
  
  // 储能配置
  energyStorage: {
    capacity: 30,               // kWh 容量
    initialSoc: 50,             // % 初始SOC
    socMin: 10,                 // % SOC下限
    socMax: 90,                 // % SOC上限
  },
  
  // 电解槽配置
  electrolyzers: [
    { id: 1, capacity: 15, count: 1, name: '15kW电解槽1' },
    { id: 2, capacity: 15, count: 1, name: '15kW电解槽2' },
    { id: 3, capacity: 10, count: 1, name: '10kW电解槽' },
    { id: 4, capacity: 5, count: 1, name: '5kW电解槽' }
  ],
  
  // 更多配置...
}
```

## API接口

### 获取仿真结果
```
GET /api/simulation/:id
```

### 获取仿真列表
```
GET /api/simulations
```

### 运行新仿真
```
POST /api/simulation/run
Content-Type: application/json

{
  "config": {
    "simulation": { "days": 2 },
    "windTurbine": { "capacity": 50 }
  }
}
```

### 获取默认配置
```
GET /api/config
```

## 输出文件

仿真结果保存在 `backend/output/` 目录下，文件名为递增ID（1.json, 2.json, ...）

文件结构：
```json
{
  "id": 1,
  "timestamp": "2024-01-23T10:00:00Z",
  "config": {...},
  "curves": {
    "combined": {
      "timestamps": [...],
      "windSolar": [...],
      "electrolyzer": [...],
      "storage": [...],
      "grid": [...],
      "storageSoc": [...]
    },
    "individual": {
      "wind": [...],
      "solar": [...],
      "electrolyzers": [...]
    }
  },
  "statistics": {
    "hydrogenProduction": 123.45,
    "totalRenewableEnergy": 456.78,
    "gridExchangeRatio": 12.34,
    "averageStorageSoc": 65.43
  }
}
```

## 开发说明

### 添加新策略
1. 在 `backend/strategies/` 目录下创建新策略文件
2. 继承 `BaseStrategy` 类
3. 在 `simulation.js` 中引入并使用

### 修改设备模型
1. 在 `backend/models/` 目录下修改对应设备文件
2. 更新 `simulation.js` 中的设备初始化和计算逻辑

### 自定义图表
1. 修改 `frontend/src/components/CurveChart.vue`
2. 调整 ECharts 配置选项

## 技术栈

- **后端**: Node.js + Express
- **前端**: Vue 3 + Element Plus + ECharts
- **构建工具**: Vite
- **数据格式**: JSON

## 许可证

MIT License

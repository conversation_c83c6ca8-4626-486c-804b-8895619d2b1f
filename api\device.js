import { get, post, urlPrefix } from './index.js'

export const testGet = async (params)=> {
  return await get('http://10.58.2.172:9098/api/v1/cecp/capacity/getSolutionList?projectId=249', params);
}

// 1 查询可用的设备及测点
export const getDeviceAndCol = async (params)=> {
  return await post(`${urlPrefix}/model/query`, params);
}


// 2 获取实时数据
export const getRealTimeData = async (params)=> {
  return await post(`${urlPrefix}/rtdata/query`, params);
}

// 3 获取历史数据
export const getHistoryData = async (params)=> {
  return await post(`${urlPrefix}/sample/query`, params);
}

// 4 获取(遥脉)统计数据，如储能日充电量等
export const getStatData = async (params)=> {
  return await post(`${urlPrefix}/statistics/query`, params);
}

// 7 遥控命令下发
export const setControl = async (params)=> {
  return await post(`${urlPrefix}/control`, params);
}


// 8 遥调命令下发-包含开关机等
export const setAdjust = async (params)=> {
  return await post(`${urlPrefix}/teleset`, params);
}

// 9 功率曲线下发
export const setPowerCurve = async (params)=> {
  return await post(`${urlPrefix}/set_curve`, params);
}


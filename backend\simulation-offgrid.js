const fs = require('fs');
const path = require('path');
const days7_15 = require('./data/baseData_7days_15min.json');
const days1_15 = require('./data/baseData_1days_15min.json');

// console.log('数据文件加载完成');
// console.log('1天数据点数:', days1_15.metadata.totalPoints);
// console.log('7天数据点数:', days7_15.metadata.totalPoints);
// ========== 离网模式设备参数配置 ==========
const CONFIG_OFFGRID = {
  // 仿真时间配置
  simulation: {
    days: 7,                    // 仿真天数 [可配置]
    timeStep: 15,               // 时间步长(分钟)
    startTime: '2025-07-24T00:00:00Z'
  },

  // 风机配置 - 与并网模式相同
  windTurbine: {
    capacity: 30                // kW 容量 [可配置]
  },

  // 光伏配置 - 与并网模式相同
  solarPanel: {
    capacity: 30                // kW 容量 [可配置]
  },

  // 储能配置 - 离网模式专用：15kWh容量，15kW功率
  energyStorage: {
    capacity: 15,               // kWh 容量 [离网模式]
    initialSoc: 50,             // % 初始SOC [可配置]
    socMin: 10,                 // % SOC下限 [可配置]
    socMax: 90,                 // % SOC上限 [可配置]
    cRate: 1                    // 电倍率：1，即最大充放电功率 15kW
  },

  // 离网模式：无电网连接
  grid: {
    powerLimit: 0               // kW 离网模式无电网功率
  },

  // 电解槽配置 - 与并网模式相同：2台15kW + 2台10kW
  electrolyzers: [
    { id: 1, capacity: 15, name: '电解槽1', initialState: 'stopped' },
    { id: 2, capacity: 15, name: '电解槽2', initialState: 'stopped' },
    { id: 3, capacity: 10, name: '电解槽3', initialState: 'stopped' },
    { id: 4, capacity: 10, name: '电解槽4', initialState: 'stopped' }
  ],

  electrolyzerConfig: {
    minPowerRatio: 30,          // % 功率范围：30%~100%
    maxPowerRatio: 100,         // % 功率范围：30%~100%
    standbyPowerRatio: 10,      // % 热备：保持10%功率即可
    energyConsumption: 4.3,     // kWh/Nm³ 能耗 [可配置]
    coldStartTime: 30           // 分钟 冷启动时间：30min
  }
};



// ========== 电解槽控制逻辑 ==========
// 严格按照并网策略.md中的控制策略实现

// 电解槽状态枚举
const ElectrolyzerState = {
  STOPPED: 'stopped',           // 停机：功率 < 10%，功率为0
  COLD_START: 'cold_start',     // 冷启动：关机->正常运行，30min功率爬坡
  STANDBY: 'standby',           // 热备：保持10%功率，产氢量不计
  RUNNING: 'running'            // 正常运行：30% <= 功率 <= 100%
};

// 初始化电解槽状态
function initializeElectrolyzers(config) {
  return config.electrolyzers.map(e => ({
    ...e,
    state: e.initialState === 'running' ? ElectrolyzerState.RUNNING : ElectrolyzerState.STOPPED,
    currentPower: 0,
    targetPower: 0,
    coldStartTimer: 0,
    hydrogenProduction: 0
  }));
}

/**
 * 电解槽群控策略 - 严格按照并网策略.md要求
 * 控制策略：
 * 1. 计算总可用功率（绿电+储能，离网模式无电网）
 * 2. 严格等比例分配功率给所有电解槽
 * 3. 如果均分后功率 >= 30%，所有电解槽等比例运行
 * 4. 如果均分后功率 < 30%，小功率电解槽优先热备或停机
 */
function allocateElectrolyzerPower(totalAvailablePower, electrolyzers, config) {
  const totalCapacity = electrolyzers.reduce((sum, e) => sum + e.capacity, 0);
  const minPowerRatio = config.electrolyzerConfig.minPowerRatio / 100; // 30%
  const standbyPowerRatio = config.electrolyzerConfig.standbyPowerRatio / 100; // 10%
  const maxPowerRatio = config.electrolyzerConfig.maxPowerRatio / 100; // 100%

  // 重置所有电解槽的目标功率
  electrolyzers.forEach(e => e.targetPower = 0);

  // 计算如果所有电解槽等比例分配的功率比例
  const equalPowerRatio = totalAvailablePower / totalCapacity;

  let runningElectrolyzers = [];
  let standbyElectrolyzers = [];
  let stoppedElectrolyzers = [];

  if (equalPowerRatio >= minPowerRatio) {
    // 所有电解槽都可以正常运行
    runningElectrolyzers = [...electrolyzers];
    const actualPowerRatio = Math.min(equalPowerRatio, maxPowerRatio);
    
    runningElectrolyzers.forEach(e => {
      e.targetPower = e.capacity * actualPowerRatio;
    });
  } else {
    // 功率不足，需要选择性运行电解槽
    // 按容量从小到大排序，优先让小功率电解槽热备或停机
    const sortedElectrolyzers = [...electrolyzers].sort((a, b) => a.capacity - b.capacity);
    
    // 从所有电解槽开始，逐步移除小功率的电解槽
    let candidateElectrolyzers = [...electrolyzers];
    let availablePowerForRunning = totalAvailablePower;

    while (candidateElectrolyzers.length > 0) {
      const candidateCapacity = candidateElectrolyzers.reduce((sum, e) => sum + e.capacity, 0);
      const requiredMinPower = candidateCapacity * minPowerRatio;

      if (availablePowerForRunning >= requiredMinPower) {
        // 找到了可以运行的电解槽组合
        runningElectrolyzers = [...candidateElectrolyzers];

        // 为运行的电解槽等比例分配功率
        const runningPowerRatio = availablePowerForRunning / candidateCapacity;
        runningElectrolyzers.forEach(e => {
          e.targetPower = e.capacity * Math.min(runningPowerRatio, maxPowerRatio);
        });

        // 处理剩余的电解槽（热备或停机）
        const remainingElectrolyzers = sortedElectrolyzers.filter(e => !runningElectrolyzers.includes(e));
        let availablePowerForStandby = totalAvailablePower - runningElectrolyzers.reduce((sum, e) => sum + e.targetPower, 0);

        for (const e of remainingElectrolyzers) {
          const standbyPower = e.capacity * standbyPowerRatio;
          if (availablePowerForStandby >= standbyPower) {
            standbyElectrolyzers.push(e);
            e.targetPower = standbyPower;
            availablePowerForStandby -= standbyPower;
          } else {
            stoppedElectrolyzers.push(e);
            e.targetPower = 0;
          }
        }
        break;
      } else {
        // 移除最小的电解槽，为其预留热备功率
        if (candidateElectrolyzers.length === 0) {
          break; // 没有更多电解槽可以移除
        }
        
        const smallestElectrolyzer = sortedElectrolyzers.find(e => candidateElectrolyzers.includes(e));
        if (smallestElectrolyzer) {
          candidateElectrolyzers = candidateElectrolyzers.filter(e => e !== smallestElectrolyzer);
          // 为热备预留功率
          const standbyPower = smallestElectrolyzer.capacity * standbyPowerRatio;
          availablePowerForRunning -= standbyPower;
        } else {
          // 找不到匹配的电解槽，退出循环
          break;
        }
      }
    }

    // 如果没有电解槽可以运行，全部停机
    if (runningElectrolyzers.length === 0) {
      stoppedElectrolyzers = [...electrolyzers];
    }
  }

  const totalAllocated = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);
  
  return {
    totalAllocated,
    runningElectrolyzers,
    standbyElectrolyzers,
    stoppedElectrolyzers
  };
}

// ========== 主仿真函数 ==========
function runOffgridSimulation(config) {
  console.log('开始加载基础数据...');
  console.log('配置天数:', config.simulation.days);
  
  // 选择对应天数的数据
  const baseData = config.simulation.days === 1 ? days1_15 : days7_15;
  console.log(`开始离网仿真，共 ${baseData.timestamps.length} 个时间点...`);
  
  // 初始化电解槽状态
  const electrolyzers = initializeElectrolyzers(config);
  
  // 初始化储能状态
  let storageSoc = config.energyStorage.initialSoc; // %
  
  // 结果存储
  const results = {
    timestamps: [...baseData.timestamps],
    windPower: [],
    solarPower: [],
    totalRenewable: [],
    electrolyzerPower: [],
    storagePower: [],
    gridPower: [], // 离网模式始终为0
    storageSoc: [],
    hydrogenProduction: [],
    electrolyzerIndividual: electrolyzers.map(() => [])
  };
  
  // 仿真循环
  for (let t = 0; t < baseData.timestamps.length; t++) {
    // 计算实际功率（标幺值 × 容量）
    const windPower = baseData.windPowerCurve[t] * config.windTurbine.capacity;
    const solarPower = baseData.solarPowerCurve[t] * config.solarPanel.capacity;
    const totalRenewable = windPower + solarPower;

    results.windPower.push(windPower);
    results.solarPower.push(solarPower);
    results.totalRenewable.push(totalRenewable);

    // 先记录当前时刻的SOC（反映上一时刻的储能功率影响）
    results.storageSoc.push(storageSoc);

    // 离网模式能量分配策略：绿电优先供给电解槽，储能调节
    const totalCapacity = electrolyzers.reduce((sum, e) => sum + e.capacity, 0);
    const minTotalPower = totalCapacity * (config.electrolyzerConfig.minPowerRatio / 100); // 所有电解槽30%总功率

    // 检查当前是否处于冷启动阶段
    const coldStartElectrolyzers = electrolyzers.filter(e => e.state === ElectrolyzerState.COLD_START);
    const stoppedElectrolyzers = electrolyzers.filter(e => e.state === ElectrolyzerState.STOPPED);

    // 预判：如果有停机电解槽且绿电足够，它们会进入冷启动状态
    const minPowerPerElectrolyzer = totalCapacity * (config.electrolyzerConfig.minPowerRatio / 100) / electrolyzers.length;
    const willEnterColdStart = stoppedElectrolyzers.length > 0 && totalRenewable >= minPowerPerElectrolyzer;

    // 判断是否处于冷启动阶段（已有冷启动的或即将进入冷启动的）
    const isInColdStartPhase = coldStartElectrolyzers.length > 0 || willEnterColdStart;

    let storagePower = 0;
    let gridPower = 0; // 离网模式始终为0
    let totalElectrolyzerDemand = 0;

    // 计算储能最大可放电功率
    const availableEnergy = (storageSoc - config.energyStorage.socMin) / 100 * config.energyStorage.capacity; // kWh
    const maxDischargeByEnergy = availableEnergy / (config.simulation.timeStep / 60); // kW
    const maxStorageDischarge = Math.min(
      config.energyStorage.capacity * config.energyStorage.cRate, // 最大放电功率 = 容量 * 电倍率
      maxDischargeByEnergy
    );

    // 计算储能最大可充电功率
    const availableCapacity = (config.energyStorage.socMax - storageSoc) / 100 * config.energyStorage.capacity; // kWh
    const maxChargeByCapacity = availableCapacity / (config.simulation.timeStep / 60); // kW
    const maxStorageCharge = Math.min(
      config.energyStorage.capacity * config.energyStorage.cRate, // 最大充电功率 = 容量 * 电倍率
      maxChargeByCapacity
    );

    // 冷启动阶段特殊处理：储能功率为0，仅使用绿电
    if (isInColdStartPhase) {
      storagePower = 0; // 冷启动阶段储能不充电也不放电

      // 离网模式：仅使用绿电，无电网支撑
      const availablePowerForElectrolyzers = totalRenewable;

      // 计算电解槽功率分配（受限于绿电）
      const powerRatio = Math.min(availablePowerForElectrolyzers / totalCapacity, config.electrolyzerConfig.maxPowerRatio / 100);
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });
      totalElectrolyzerDemand = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);
    } else if (totalRenewable >= minTotalPower) {
      // 绿电足够让所有电解槽达到30%以上
      // 电解槽尽量多用绿电（30%-100%之间）
      const maxElectrolyzerPower = totalCapacity * (config.electrolyzerConfig.maxPowerRatio / 100);
      totalElectrolyzerDemand = Math.min(totalRenewable, maxElectrolyzerPower);
      
      // 等比例分配给所有电解槽
      const powerRatio = totalElectrolyzerDemand / totalCapacity;
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });
      
      // 剩余绿电优先储能充电
      const surplusPower = totalRenewable - totalElectrolyzerDemand;
      if (surplusPower > 0) {
        const chargePower = Math.min(surplusPower, maxStorageCharge);
        storagePower = -chargePower; // 充电为负
        
        // 离网模式：储能充满后剩余电力无法上网，只能弃电
        // 这里不需要额外处理，剩余电力自然被弃掉
      }
    } else {
      // 绿电不足，需要储能补充
      const powerShortage = minTotalPower - totalRenewable;
      
      // 储能补充（离网模式无电网后备）
      const storageSupply = Math.min(powerShortage, maxStorageDischarge);
      storagePower = storageSupply; // 放电为正
      
      // 计算实际可用功率并分配电解槽
      const actualAvailablePower = totalRenewable + storageSupply;
      
      // 简化版本：直接等比例分配，避免复杂的while循环
      const powerRatio = Math.min(actualAvailablePower / totalCapacity, config.electrolyzerConfig.maxPowerRatio / 100);
      electrolyzers.forEach(e => {
        e.targetPower = e.capacity * powerRatio;
      });
      totalElectrolyzerDemand = electrolyzers.reduce((sum, e) => sum + e.targetPower, 0);
    }

    // 更新电解槽状态和计算产氢量
    let totalElectrolyzerPower = 0;
    let stepHydrogen = 0;

    electrolyzers.forEach((e, index) => {
      // 更新电解槽功率
      e.currentPower = e.targetPower;
      totalElectrolyzerPower += e.currentPower;
      
      // 计算产氢量（只有正常运行状态才产氢）
      if (e.currentPower >= e.capacity * config.electrolyzerConfig.minPowerRatio / 100) {
        const hydrogenRate = e.currentPower / config.electrolyzerConfig.energyConsumption; // Nm³/h
        const stepHydrogenNm3 = hydrogenRate * (config.simulation.timeStep / 60); // Nm³
        const stepHydrogenKg = stepHydrogenNm3 * 0.08988; // kg (1 Nm³ H2 = 0.08988 kg)
        stepHydrogen += stepHydrogenKg;
        e.hydrogenProduction += stepHydrogenKg;
      }
      
      // 记录个体电解槽功率
      results.electrolyzerIndividual[index].push(e.currentPower);
    });

    // 更新储能SOC
    const energyChange = storagePower * (config.simulation.timeStep / 60); // kWh
    storageSoc -= (energyChange / config.energyStorage.capacity) * 100; // %
    storageSoc = Math.max(config.energyStorage.socMin, Math.min(config.energyStorage.socMax, storageSoc));

    // 记录结果
    results.electrolyzerPower.push(totalElectrolyzerPower);
    results.storagePower.push(storagePower);
    results.gridPower.push(gridPower); // 离网模式始终为0
    results.hydrogenProduction.push(stepHydrogen);
  }

  return results;
}

// ========== 文件管理 ==========
// 获取下一个离网仿真文件ID
function getNextOffgridFileId() {
  const outputDir = path.join(__dirname, 'off_grid');

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // 读取现有文件，找到最大ID
  const files = fs.readdirSync(outputDir);
  const jsonFiles = files.filter(f => f.endsWith('.json'));

  if (jsonFiles.length === 0) {
    return 1;
  }

  const ids = jsonFiles.map(f => {
    const match = f.match(/^(\d+)\.json$/);
    return match ? parseInt(match[1]) : 0;
  });

  return Math.max(...ids) + 1;
}

// 保存离网仿真结果
function saveOffgridResults(results, config) {
  const fileId = getNextOffgridFileId();
  const outputPath = path.join(__dirname, 'off_grid', `${fileId}.json`);

  // 计算统计数据
  const totalRenewableEnergy = results.totalRenewable.reduce((sum, power) =>
    sum + power * (config.simulation.timeStep / 60), 0);
  const totalGridEnergy = 0; // 离网模式无电网交换
  const gridExchangeRatio = 0; // 离网模式无电网交换

  const output = {
    id: fileId,
    timestamp: new Date().toISOString(),
    mode: 'offgrid', // 标识离网模式
    config: config,
    curves: {
      combined: {
        timestamps: results.timestamps,
        windSolar: results.totalRenewable,
        electrolyzer: results.electrolyzerPower,
        storage: results.storagePower,
        grid: results.gridPower, // 始终为0
        storageSoc: results.storageSoc
      },
      individual: {
        wind: results.windPower,
        solar: results.solarPower,
        electrolyzers: results.electrolyzerIndividual.map((data, index) => ({
          name: config.electrolyzers[index].name,
          data: data
        }))
      }
    },
    statistics: {
      hydrogenProduction: results.hydrogenProduction[results.hydrogenProduction.length - 1],
      totalRenewableEnergy: totalRenewableEnergy,
      totalGridEnergy: totalGridEnergy,
      gridExchangeRatio: gridExchangeRatio,
      averageStorageSoc: results.storageSoc.reduce((sum, soc) => sum + soc, 0) / results.storageSoc.length
    }
  };

  fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));
  console.log(`离网仿真结果已保存到: ${outputPath}`);
  console.log(`文件ID: ${fileId}`);
  console.log(`产氢量: ${output.statistics.hydrogenProduction.toFixed(2)} kg`);
  console.log(`绿电利用率: 100% (离网模式)`);
  console.log(`平均储能SOC: ${output.statistics.averageStorageSoc.toFixed(1)}%`);

  return fileId;
}

// ========== 主程序 ==========
function mainOffgrid() {
  console.log('开始运行离网风光储制氢仿真...');
  console.log('配置参数:', JSON.stringify(CONFIG_OFFGRID, null, 2));

  const results = runOffgridSimulation(CONFIG_OFFGRID);
  const fileId = saveOffgridResults(results, CONFIG_OFFGRID);

  console.log('离网仿真完成！');
  return fileId;
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('开始执行离网仿真main函数...');
  try {
    mainOffgrid();
    console.log('离网仿真main函数执行完成');
  } catch (error) {
    console.error('离网仿真main函数执行出错:', error);
  }
}

module.exports = { runOffgridSimulation, CONFIG_OFFGRID, mainOffgrid, saveOffgridResults };

import {
  setPowerCurve, setAdjust, getRealTimeData,
  getHistoryData, getStatData, setControl
} from '../api/device.js'

/**
 * 开关机
 */
async function openAndClose(deviceName, openStatus) {
  const deviceNameList = {
    ALK_15_1: { device_id: 5 },
    ALK_15_2: { device_id: 6 },
    ALK_10_1: { device_id: 7 },
    ALK_10_2: { device_id: 8 }
  }
  const openStatusList = {
    CLOSE: 85,
    OPEN: 170
  }
  const alkParams = {
    "model": "pub_energy_consumer",
    "device_id": deviceNameList[deviceName].device_id,
    "col": "DJCYT7",
    "value": openStatusList[openStatus]
  }
  const data = await setAdjust(alkParams)
  console.log(`${deviceName} ${openStatus}:`, data)
}
// demo
openAndClose('ALK_15_1', 'CLOSE') // TOOD: 只有电解槽本身，还需先开直流源？关的时候先关直流源，以及关之前先功率设为0？

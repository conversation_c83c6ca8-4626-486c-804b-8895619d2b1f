import axios from "axios";

const request = axios.create({
  baseURL: 'http://10.58.0.15:32435',
  // baseURL: '/',
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    "accessSecret": "21ED2C5B509F49FF8E323C67B9A2B084"
  },
  withCredentials: true, // 跨域请求时是否需要使用凭证
  timeout: 600000, // 请求超时时间
  // transformResponse: [
  //   data => {
  //     // console.log('axios data:', data)
  //     return JSON.parse(data);
  //   }
  // ]
});

// 错误处理函数
function errorHandle(data) {
  const res = data?.response
  if (res?.status > 400 && res?.status < 600) {
    console.error(res?.statusText)
  }
}
// 成功处理函数

function successHandle(response) {
  if (response.status >= 200 && response.status < 400) {
    return {
      ...response.data,
      data: response.data?.data || {}
    };
  }
}
// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // console.log('Request:', JSON.stringify(config, null, 2))
    return config;
  },
  (error) => {
    // 错误抛到业务代码
    error.data = {};
    error.data.msg = "服务器异常";
    return Promise.resolve(error);
  }
);

request.interceptors.response.use(
  (response) => {
    // console.log('Response:', JSON.stringify(response, null, 2))
    return successHandle(response);
  },
  (err) => {
    errorHandle(err);
  }
);

export default request;




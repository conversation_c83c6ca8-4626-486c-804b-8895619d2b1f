import numpy as np

# 更新系统参数配置
PV_MAX_POWER = 30  # 光伏最大功率 (kW)
WIND_MAX_POWER = 30  # 风机最大功率 (kW)
ESS_CAPACITY = 30  # 储能系统容量 (kWh)
ESS_MIN_SOC = 0.1  # 储能最小SOC (10%)
ESS_MAX_SOC = 0.9  # 储能最大SOC (90%)
GRID_MAX_POWER = 20  # 电网最大消纳功率 (kW)
TIME_STEPS = 48  # 12小时数据点(每15分钟一个点)
DT = 0.25  # 时间步长(小时)

# 电解槽配置
ELECTROLYZERS = [
    {"id": 0, "rated_power": 15, "min_ratio": 0.3, "status": True},
    {"id": 1, "rated_power": 15, "min_ratio": 0.3, "status": True},
    {"id": 2, "rated_power": 10, "min_ratio": 0.3, "status": True},
    {"id": 3, "rated_power": 10, "min_ratio": 0.3, "status": True},
]

class HydrogenSystemController:
    def __init__(self):
        # 系统状态初始化
        self.ess_soc = 0.5  # 初始SOC设为50%
        self.electrolyzers = ELECTROLYZERS.copy()
    
    # API调用: 获取风光预测数据(伪代码)
    def get_forecast_data(self):
        pv_forecast = np.random.uniform(0, PV_MAX_POWER, TIME_STEPS)  # 光伏预测功率
        wind_forecast = np.random.uniform(0, WIND_MAX_POWER, TIME_STEPS)  # 风机预测功率
        return pv_forecast, wind_forecast
    
    # API调用: 获取当前储能SOC(伪代码)
    def get_ess_soc(self):
        return self.ess_soc
    
    # API调用: 设置储能充放电功率(伪代码)
    # power > 0 : 充电
    # power < 0 : 放电
    def set_ess_power(self, power):
        # 充放电功率限制
        max_charge_power = 10  # 最大充电功率 (kW)
        max_discharge_power = 10  # 最大放电功率 (kW)
        
        # 应用功率限制
        if power > max_charge_power:
            power = max_charge_power
        elif power < -max_discharge_power:
            power = -max_discharge_power
            
        # 更新SOC (实际应用中通过API下发指令)
        self.ess_soc += (power * DT) / ESS_CAPACITY
        self.ess_soc = max(ESS_MIN_SOC, min(ESS_MAX_SOC, self.ess_soc))
        return power

    # API调用: 设置电解槽功率(伪代码)
    def set_electrolyzer_power(self, electrolyzer_id, power):
        for e in self.electrolyzers:
            if e["id"] == electrolyzer_id:
                rated_power = e["rated_power"]
                min_power = rated_power * e["min_ratio"]
                # 功率限制保护 (30%-100%范围)
                power = max(min_power, min(rated_power, power))
                e["current_power"] = power
                return power
        return 0
    
    # API调用: 设置电解槽启停状态(伪代码)
    def set_electrolyzer_status(self, electrolyzer_id, status):
        for e in self.electrolyzers:
            if e["id"] == electrolyzer_id:
                e["status"] = status
                if not status:
                    e["current_power"] = 0
                    
    def calc_min_total_power(self):
        """计算所有开启电解槽的最小总功率"""
        total = 0
        for e in self.electrolyzers:
            if e["status"]:
                total += e["rated_power"] * e["min_ratio"]
        return total
    
    def calc_max_total_power(self):
        """计算所有开启电解槽的最大总功率"""
        total = 0
        for e in self.electrolyzers:
            if e["status"]:
                total += e["rated_power"]
        return total
    
    def distribute_power(self, available_power):
        """分配可用功率给电解槽"""
        # 1. 首先满足所有电解槽的最小功率需求
        min_total = self.calc_min_total_power()
        if available_power < min_total:
            # 功率不足，所有电解槽按最小功率运行
            for e in self.electrolyzers:
                if e["status"]:
                    self.set_electrolyzer_power(e["id"], e["rated_power"] * e["min_ratio"])
            return min_total
        
        # 2. 分配剩余功率
        remaining_power = available_power - min_total
        max_total = self.calc_max_total_power() - min_total
        
        # 计算总额定功率
        total_rated = sum(e["rated_power"] for e in self.electrolyzers if e["status"])
        
        # 按额定功率比例分配剩余功率
        for e in self.electrolyzers:
            if e["status"]:
                share = remaining_power * (e["rated_power"] / total_rated)
                allocated = min(share, e["rated_power"] - (e["rated_power"] * e["min_ratio"]))
                power_level = e["rated_power"] * e["min_ratio"] + allocated
                self.set_electrolyzer_power(e["id"], power_level)
        
        return available_power
    
    def control_strategy(self):
        """风光储网电解槽控制主策略"""
        # 获取预测数据
        pv_forecast, wind_forecast = self.get_forecast_data()
        total_green_power = pv_forecast + wind_forecast
        
        # 获取当前储能状态
        self.ess_soc = self.get_ess_soc()
        
        # 初始化控制决策
        ess_powers = np.zeros(TIME_STEPS)
        grid_interactions = np.zeros(TIME_STEPS)  # 正值:从电网购电, 负值:向电网售电
        
        # 按时间步执行控制策略
        for t in range(TIME_STEPS):
            # 计算当前可用的绿电功率
            green_power = min(total_green_power[t], PV_MAX_POWER + WIND_MAX_POWER)
            
            # 计算所需的最小总功率 (所有电解槽以最小功率30%运行)
            min_total_power = self.calc_min_total_power()
            max_total_power = self.calc_max_total_power()
            
            # 1: 绿电充足 (电解槽功率分配后 >= 30%)
            if green_power >= min_total_power:
                # 分配绿电给电解槽
                allocated = self.distribute_power(green_power)
                
                # 计算剩余绿电
                surplus_green = green_power - allocated
                
                # 剩余绿电分配 (先储能后电网)
                if surplus_green > 0:
                    # 计算储能可充电量
                    charge_capacity = min(10, (ESS_MAX_SOC - self.ess_soc) * ESS_CAPACITY / DT)
                    charge_power = min(surplus_green, charge_capacity)
                    ess_powers[t] = self.set_ess_power(charge_power)
                    
                    # 剩余部分上网（卖给电网），不超过20kW限制
                    grid_sell = min(surplus_green - charge_power, GRID_MAX_POWER)
                    grid_interactions[t] = -grid_sell  # 负值表示卖给电网
            
            # 2: 绿电不足 (电解槽功率分配后 < 30%)
            else:
                # 功率缺口
                power_deficit = min_total_power - green_power
                available_power = green_power
                
                # 尝试用储能补充
                if power_deficit > 0 and self.ess_soc > ESS_MIN_SOC:
                    # 计算储能可放电量
                    discharge_capacity = min(10, (self.ess_soc - ESS_MIN_SOC) * ESS_CAPACITY / DT)
                    discharge_power = min(power_deficit, discharge_capacity)
                    ess_powers[t] = self.set_ess_power(-discharge_power)
                    available_power += discharge_power
                    power_deficit -= discharge_power
                
                # 如果仍有功率缺口，尝试从电网购电
                if power_deficit > 0:
                    # 电网购电不超过20kW限制
                    grid_buy = min(power_deficit, GRID_MAX_POWER)
                    grid_interactions[t] = grid_buy  # 正值表示从电网购电
                    available_power += grid_buy
                
                # 分配可用功率给电解槽 (确保所有电解槽以最小功率运行)
                self.distribute_power(available_power)
            
            # 保护机制检查
            self.safety_checks(t)
    
    def safety_checks(self, time_step):
        """系统安全保护机制"""
        # 电解槽功率保护
        for e in self.electrolyzers:
            if e["status"]:
                min_power = e["rated_power"] * e["min_ratio"]
                max_power = e["rated_power"]
                current = e.get("current_power", 0)
                
                # 确保功率在30%-100%范围内
                if current < min_power or current > max_power:
                    safe_power = max(min_power, min(max_power, current))
                    self.set_electrolyzer_power(e["id"], safe_power)
        
        # 储能SOC保护
        if self.ess_soc < ESS_MIN_SOC + 0.05:  # 接近下限10%
            self.set_ess_power(0)  # 停止放电
        elif self.ess_soc > ESS_MAX_SOC - 0.05:  # 接近上限90%
            self.set_ess_power(0)  # 停止充电
        
        
        # 温度等保护伪代码
        # for e in self.electrolyzers:
        #    if get_temperature(e["id"]) > MAX_TEMP:
        #        reduce_power(e["id"], SAFE_REDUCTION)
    
    def visualize_results(self):
        """获取各模块运行的功率曲线数据"""
        pass

# 主程序
if __name__ == "__main__":
    controller = HydrogenSystemController()
    controller.control_strategy()
    controller.visualize_results()